# EAB 0023.HK Force Update PowerShell Script
param(
    [string]$Server = "localhost",
    [string]$Database = "finance", 
    [string]$Username = "root",
    [string]$Password = "",
    [string]$Symbol = "0023.HK",
    [int]$Days = 7
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "EAB 0023.HK Force Update" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "Found .NET version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: .NET SDK not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Build project
Write-Host "Building project..." -ForegroundColor Yellow
try {
    dotnet build --configuration Release --verbosity quiet
    Write-Host "Build successful!" -ForegroundColor Green
} catch {
    Write-Host "Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Force Update Configuration:" -ForegroundColor Yellow
Write-Host "Server: $Server"
Write-Host "Database: $Database"
Write-Host "Username: $Username"
Write-Host "Symbol: $Symbol"
Write-Host "Days to update: $Days"
Write-Host ""

# Get password if not provided
if ([string]::IsNullOrEmpty($Password)) {
    $SecurePassword = Read-Host "Enter MySQL password" -AsSecureString
    $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
}

Write-Host "WARNING: This will re-download the last $Days days of data!" -ForegroundColor Red
$confirm = Read-Host "Continue? (y/N)"

if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "Starting force update..." -ForegroundColor Green
Write-Host ""

# Run the force update
$arguments = @(
    "run", "--configuration", "Release", "console",
    "--server", $Server,
    "--database", $Database, 
    "--username", $Username,
    "--password", $Password,
    "--symbol", $Symbol,
    "--days", $Days,
    "--force"
)

try {
    $process = Start-Process -FilePath "dotnet" -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    Write-Host ""
    if ($process.ExitCode -eq 0) {
        Write-Host "SUCCESS: Force update completed!" -ForegroundColor Green
        Write-Host "You should now have the missing data including July 29th." -ForegroundColor Green
    } else {
        Write-Host "FAILED: Force update failed with exit code $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: Failed to run update process: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
