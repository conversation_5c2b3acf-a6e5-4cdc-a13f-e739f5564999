@echo off
echo ========================================
echo EAB 0023.HK 指标计算测试
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    pause
    exit /b 1
)

echo 正在编译项目...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 运行指标计算测试...
echo.

dotnet run --configuration Release console --test-indicators

echo.
echo 测试完成！
echo.
echo 说明:
echo - Controller: 1当Close^>MidPrice，否则为0
echo - Full_Y: Controller为1的总数/当前行数
echo - E: (8*MFI/100-3)*Full_Y - 3*MFI/100 + 1
echo - i: 当前行数（从1开始）
echo.
pause
