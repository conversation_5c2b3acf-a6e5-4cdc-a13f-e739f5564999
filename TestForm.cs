using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using EABDataUpdater.Services;

namespace EABDataUpdater
{
    /// <summary>
    /// 测试窗体 - 用于测试各种连接和功能
    /// </summary>
    public partial class TestForm : Form
    {
        private Button btnTestDatabase;
        private Button btnTestYahooFinance;
        private Button btnGetStockInfo;
        private Button btnGetQuote;
        private Button btnCheckTable;
        private TextBox txtLog;
        private Label lblStatus;
        private ProgressBar progressBar;
        private GroupBox groupBoxConfig;
        private TextBox txtServer;
        private TextBox txtDatabase;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private TextBox txtSymbol;

        public TestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 主窗体设置
            this.Text = "EAB数据更新器 - 连接测试";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 500);

            // 配置组框
            groupBoxConfig = new GroupBox
            {
                Text = "配置信息",
                Location = new Point(12, 12),
                Size = new Size(760, 120),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // 服务器
            var lblServer = new Label { Text = "服务器:", Location = new Point(15, 25), Size = new Size(60, 23) };
            txtServer = new TextBox { Text = "localhost", Location = new Point(80, 22), Size = new Size(120, 23) };

            // 数据库
            var lblDatabase = new Label { Text = "数据库:", Location = new Point(220, 25), Size = new Size(60, 23) };
            txtDatabase = new TextBox { Text = "finance", Location = new Point(285, 22), Size = new Size(120, 23) };

            // 股票代码
            var lblSymbol = new Label { Text = "股票代码:", Location = new Point(425, 25), Size = new Size(70, 23) };
            txtSymbol = new TextBox { Text = "0023.HK", Location = new Point(500, 22), Size = new Size(100, 23) };

            // 用户名
            var lblUsername = new Label { Text = "用户名:", Location = new Point(15, 55), Size = new Size(60, 23) };
            txtUsername = new TextBox { Text = "root", Location = new Point(80, 52), Size = new Size(120, 23) };

            // 密码
            var lblPassword = new Label { Text = "密码:", Location = new Point(220, 55), Size = new Size(60, 23) };
            txtPassword = new TextBox { UseSystemPasswordChar = true, Location = new Point(285, 52), Size = new Size(120, 23) };

            groupBoxConfig.Controls.AddRange(new Control[] 
            { 
                lblServer, txtServer, lblDatabase, txtDatabase, lblSymbol, txtSymbol,
                lblUsername, txtUsername, lblPassword, txtPassword
            });

            // 测试按钮组
            btnTestDatabase = new Button
            {
                Text = "测试数据库",
                Location = new Point(12, 145),
                Size = new Size(100, 30),
                BackColor = Color.LightBlue
            };
            btnTestDatabase.Click += BtnTestDatabase_Click;

            btnTestYahooFinance = new Button
            {
                Text = "测试Yahoo Finance",
                Location = new Point(125, 145),
                Size = new Size(130, 30),
                BackColor = Color.LightGreen
            };
            btnTestYahooFinance.Click += BtnTestYahooFinance_Click;

            btnGetStockInfo = new Button
            {
                Text = "获取股票信息",
                Location = new Point(268, 145),
                Size = new Size(100, 30),
                BackColor = Color.LightYellow
            };
            btnGetStockInfo.Click += BtnGetStockInfo_Click;

            btnGetQuote = new Button
            {
                Text = "获取最新报价",
                Location = new Point(381, 145),
                Size = new Size(100, 30),
                BackColor = Color.LightCoral
            };
            btnGetQuote.Click += BtnGetQuote_Click;

            btnCheckTable = new Button
            {
                Text = "检查表结构",
                Location = new Point(494, 145),
                Size = new Size(100, 30),
                BackColor = Color.LightSalmon
            };
            btnCheckTable.Click += BtnCheckTable_Click;

            // 状态标签
            lblStatus = new Label
            {
                Text = "就绪",
                Location = new Point(12, 185),
                Size = new Size(600, 23),
                ForeColor = Color.Blue
            };

            // 进度条
            progressBar = new ProgressBar
            {
                Location = new Point(12, 215),
                Size = new Size(760, 23),
                Style = ProgressBarStyle.Marquee,
                Visible = false,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // 日志文本框
            txtLog = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Location = new Point(12, 250),
                Size = new Size(760, 400),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] 
            { 
                groupBoxConfig, btnTestDatabase, btnTestYahooFinance, btnGetStockInfo, 
                btnGetQuote, btnCheckTable, lblStatus, progressBar, txtLog 
            });

            this.ResumeLayout(false);
        }

        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            txtLog.AppendText($"{DateTime.Now:HH:mm:ss} - {message}{Environment.NewLine}");
            txtLog.ScrollToCaret();
        }

        private void SetStatus(string message, Color color)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, Color>(SetStatus), message, color);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = color;
        }

        private async void BtnTestDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                progressBar.Visible = true;
                SetStatus("正在测试数据库连接...", Color.Blue);
                
                var connectionString = GetConnectionString();
                using (var dbService = new DatabaseService(connectionString))
                {
                    dbService.OnStatusUpdate += (s, msg) => AppendLog($"[DB] {msg}");
                    dbService.OnError += (s, err) => AppendLog($"[DB ERROR] {err}");
                    
                    var success = await dbService.TestConnectionAsync();
                    
                    if (success)
                    {
                        SetStatus("数据库连接测试成功", Color.Green);
                    }
                    else
                    {
                        SetStatus("数据库连接测试失败", Color.Red);
                    }
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[EXCEPTION] {ex.Message}");
                SetStatus("数据库连接测试异常", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async void BtnTestYahooFinance_Click(object sender, EventArgs e)
        {
            try
            {
                progressBar.Visible = true;
                SetStatus("正在测试Yahoo Finance连接...", Color.Blue);
                
                using (var yfService = new YahooFinanceService(txtSymbol.Text))
                {
                    yfService.OnStatusUpdate += (s, msg) => AppendLog($"[YF] {msg}");
                    yfService.OnError += (s, err) => AppendLog($"[YF ERROR] {err}");
                    
                    var success = await yfService.TestConnectionAsync();
                    
                    if (success)
                    {
                        SetStatus("Yahoo Finance连接测试成功", Color.Green);
                    }
                    else
                    {
                        SetStatus("Yahoo Finance连接测试失败", Color.Red);
                    }
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[EXCEPTION] {ex.Message}");
                SetStatus("Yahoo Finance连接测试异常", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async void BtnGetStockInfo_Click(object sender, EventArgs e)
        {
            try
            {
                progressBar.Visible = true;
                SetStatus("正在获取股票信息...", Color.Blue);
                
                using (var yfService = new YahooFinanceService(txtSymbol.Text))
                {
                    yfService.OnStatusUpdate += (s, msg) => AppendLog($"[YF] {msg}");
                    yfService.OnError += (s, err) => AppendLog($"[YF ERROR] {err}");
                    
                    var stockInfo = await yfService.GetStockInfoAsync();
                    
                    AppendLog($"股票代码: {stockInfo.Symbol}");
                    AppendLog($"股票名称: {stockInfo.LongName ?? stockInfo.ShortName}");
                    AppendLog($"交易所: {stockInfo.Exchange}");
                    AppendLog($"市场: {stockInfo.Market}");
                    
                    SetStatus("股票信息获取成功", Color.Green);
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[EXCEPTION] {ex.Message}");
                SetStatus("股票信息获取异常", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async void BtnGetQuote_Click(object sender, EventArgs e)
        {
            try
            {
                progressBar.Visible = true;
                SetStatus("正在获取最新报价...", Color.Blue);
                
                using (var yfService = new YahooFinanceService(txtSymbol.Text))
                {
                    yfService.OnStatusUpdate += (s, msg) => AppendLog($"[YF] {msg}");
                    yfService.OnError += (s, err) => AppendLog($"[YF ERROR] {err}");
                    
                    var quote = await yfService.GetLatestQuoteAsync();
                    
                    AppendLog($"当前价格: {quote.Price:F2}");
                    AppendLog($"涨跌: {quote.Change:+0.00;-0.00;0.00} ({quote.ChangePercent:+0.00;-0.00;0.00}%)");
                    AppendLog($"成交量: {quote.Volume:N0}");
                    AppendLog($"开盘价: {quote.Open:F2}");
                    AppendLog($"最高价: {quote.DayHigh:F2}");
                    AppendLog($"最低价: {quote.DayLow:F2}");
                    AppendLog($"昨收价: {quote.PreviousClose:F2}");
                    
                    SetStatus("最新报价获取成功", Color.Green);
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[EXCEPTION] {ex.Message}");
                SetStatus("最新报价获取异常", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private async void BtnCheckTable_Click(object sender, EventArgs e)
        {
            try
            {
                progressBar.Visible = true;
                SetStatus("正在检查表结构...", Color.Blue);
                
                var connectionString = GetConnectionString();
                using (var dbService = new DatabaseService(connectionString))
                {
                    dbService.OnStatusUpdate += (s, msg) => AppendLog($"[DB] {msg}");
                    dbService.OnError += (s, err) => AppendLog($"[DB ERROR] {err}");
                    
                    var tableName = "eab_0023hk";
                    var exists = await dbService.CheckTableExistsAsync(tableName);
                    
                    if (exists)
                    {
                        var count = await dbService.GetTableRecordCountAsync(tableName);
                        var latestDate = await dbService.GetLatestDateAsync(tableName);
                        var structure = await dbService.GetTableStructureAsync(tableName);
                        
                        AppendLog($"表 {tableName} 存在");
                        AppendLog($"记录数: {count}");
                        AppendLog($"最新日期: {latestDate?.ToString("yyyy-MM-dd") ?? "无数据"}");
                        AppendLog("表结构:");
                        
                        foreach (var column in structure)
                        {
                            AppendLog($"  {column}");
                        }
                        
                        SetStatus("表结构检查完成", Color.Green);
                    }
                    else
                    {
                        AppendLog($"表 {tableName} 不存在");
                        SetStatus("表不存在", Color.Orange);
                    }
                }
            }
            catch (Exception ex)
            {
                AppendLog($"[EXCEPTION] {ex.Message}");
                SetStatus("表结构检查异常", Color.Red);
            }
            finally
            {
                progressBar.Visible = false;
            }
        }

        private string GetConnectionString()
        {
            return $"Server={txtServer.Text};Database={txtDatabase.Text};Uid={txtUsername.Text};Pwd={txtPassword.Text};CharSet=utf8mb4;SslMode=None;";
        }
    }
}
