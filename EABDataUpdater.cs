using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EABDataUpdater.Models;
using EABDataUpdater.Services;

namespace EABDataUpdater
{
    /// <summary>
    /// EAB 0023.HK 数据更新器
    /// </summary>
    public class EAB0023HKDataUpdater : IDisposable
    {
        private readonly DatabaseService _databaseService;
        private readonly YahooFinanceService _yahooFinanceService;
        private readonly string _tableName = "eab_0023hk";
        private readonly string _symbol;

        public EAB0023HKDataUpdater(string connectionString, string symbol = "0023.HK")
        {
            _symbol = symbol;
            _databaseService = new DatabaseService(connectionString);
            _yahooFinanceService = new YahooFinanceService(symbol);

            // 订阅服务事件
            _databaseService.OnStatusUpdate += (s, e) => OnStatusUpdate?.Invoke(s, e);
            _databaseService.OnError += (s, e) => OnError?.Invoke(s, e);
            _yahooFinanceService.OnStatusUpdate += (s, e) => OnStatusUpdate?.Invoke(s, e);
            _yahooFinanceService.OnError += (s, e) => OnError?.Invoke(s, e);
        }

        /// <summary>
        /// 数据更新事件
        /// </summary>
        public event EventHandler<string> OnStatusUpdate;
        public event EventHandler<string> OnError;

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            return await _databaseService.TestConnectionAsync();
        }

        /// <summary>
        /// 测试Yahoo Finance连接
        /// </summary>
        public async Task<bool> TestYahooFinanceConnectionAsync()
        {
            return await _yahooFinanceService.TestConnectionAsync();
        }

        /// <summary>
        /// 获取股票信息
        /// </summary>
        public async Task<StockInfo> GetStockInfoAsync()
        {
            return await _yahooFinanceService.GetStockInfoAsync();
        }

        /// <summary>
        /// 获取最新报价
        /// </summary>
        public async Task<StockQuote> GetLatestQuoteAsync()
        {
            return await _yahooFinanceService.GetLatestQuoteAsync();
        }

        /// <summary>
        /// 获取数据库中最新的日期
        /// </summary>
        public async Task<DateTime?> GetLatestDateFromDbAsync()
        {
            return await _databaseService.GetLatestDateAsync(_tableName);
        }

        /// <summary>
        /// 获取数据库统计信息
        /// </summary>
        public async Task<(bool tableExists, long recordCount, DateTime? latestDate)> GetDatabaseStatsAsync()
        {
            var tableExists = await _databaseService.CheckTableExistsAsync(_tableName);
            var recordCount = tableExists ? await _databaseService.GetTableRecordCountAsync(_tableName) : 0;
            var latestDate = tableExists ? await _databaseService.GetLatestDateAsync(_tableName) : null;

            return (tableExists, recordCount, latestDate);
        }

        /// <summary>
        /// 从Yahoo Finance下载数据
        /// </summary>
        public async Task<List<StockData>> DownloadYahooFinanceDataAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var stockDataList = await _yahooFinanceService.DownloadHistoricalDataAsync(startDate, endDate);

            // 计算技术指标
            if (stockDataList.Count > 0)
            {
                TechnicalIndicatorService.CalculateAllIndicators(stockDataList);
            }

            return stockDataList;
        }

        /// <summary>
        /// 插入或更新数据到数据库
        /// </summary>
        public async Task<bool> InsertOrUpdateDataAsync(List<StockData> stockDataList)
        {
            var insertQuery = $@"
                INSERT INTO {_tableName} (
                    Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
                    PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
                    Y_Value, X_Value, E_Value, RSI, TradingSignal, i, midprice,
                    Controller, Full_Y, E
                ) VALUES (
                    @Date, @Open, @High, @Low, @Close, @Volume, @TypicalPrice, @MoneyFlow,
                    @PositiveMoneyFlow, @NegativeMoneyFlow, @MoneyFlowRatio, @MFI,
                    @Y_Value, @X_Value, @E_Value, @RSI, @TradingSignal, @i, @midprice,
                    @Controller, @Full_Y, @E
                ) ON DUPLICATE KEY UPDATE
                    Open = VALUES(Open),
                    High = VALUES(High),
                    Low = VALUES(Low),
                    Close = VALUES(Close),
                    Volume = VALUES(Volume),
                    TypicalPrice = VALUES(TypicalPrice),
                    MoneyFlow = VALUES(MoneyFlow),
                    PositiveMoneyFlow = VALUES(PositiveMoneyFlow),
                    NegativeMoneyFlow = VALUES(NegativeMoneyFlow),
                    MoneyFlowRatio = VALUES(MoneyFlowRatio),
                    MFI = VALUES(MFI),
                    RSI = VALUES(RSI),
                    updated_at = CURRENT_TIMESTAMP";

            return await _databaseService.BulkInsertOrUpdateAsync(
                _tableName,
                stockDataList,
                data => data.ToParameterDictionary(),
                insertQuery);
        }

        /// <summary>
        /// 主要的数据更新存储过程（带详细统计）
        /// </summary>
        public async Task<UpdateStatistics> UpdateDataProcedureWithStatsAsync(int daysBack = 30)
        {
            var stats = new UpdateStatistics
            {
                StartTime = DateTime.Now
            };

            try
            {
                OnStatusUpdate?.Invoke(this, "开始数据更新过程...");

                // 测试连接
                if (!await TestDatabaseConnectionAsync())
                {
                    stats.Success = false;
                    stats.ErrorMessage = "数据库连接失败";
                    return stats;
                }

                if (!await TestYahooFinanceConnectionAsync())
                {
                    stats.Success = false;
                    stats.ErrorMessage = "Yahoo Finance连接失败";
                    return stats;
                }

                // 获取数据库统计信息
                var (tableExists, recordCount, latestDate) = await GetDatabaseStatsAsync();

                if (!tableExists)
                {
                    stats.Success = false;
                    stats.ErrorMessage = $"表 {_tableName} 不存在";
                    return stats;
                }

                // 确定下载的开始日期
                DateTime startDate;
                if (latestDate.HasValue)
                {
                    startDate = latestDate.Value.AddDays(1);
                    OnStatusUpdate?.Invoke(this, $"从最新日期 {latestDate:yyyy-MM-dd} 后开始更新");
                }
                else
                {
                    startDate = DateTime.Now.AddDays(-daysBack);
                    OnStatusUpdate?.Invoke(this, $"首次运行，回溯 {daysBack} 天");
                }

                // 下载数据
                var stockDataList = await DownloadYahooFinanceDataAsync(startDate);
                stats.TotalRecords = stockDataList.Count;

                if (stockDataList.Count == 0)
                {
                    OnStatusUpdate?.Invoke(this, "没有新数据需要更新");
                    stats.Success = true;
                    return stats;
                }

                // 设置统计信息
                stats.OldestDataDate = stockDataList.Min(x => x.Date);
                stats.LatestDataDate = stockDataList.Max(x => x.Date);
                stats.NewRecords = stockDataList.Count; // 简化处理，假设都是新记录

                // 插入/更新数据
                var success = await InsertOrUpdateDataAsync(stockDataList);
                stats.Success = success;

                if (success)
                {
                    OnStatusUpdate?.Invoke(this, "数据更新完成");
                }

                return stats;
            }
            catch (Exception ex)
            {
                stats.Success = false;
                stats.ErrorMessage = ex.Message;
                OnError?.Invoke(this, $"数据更新过程失败: {ex.Message}");
                return stats;
            }
            finally
            {
                stats.EndTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 简化的更新方法（保持向后兼容）
        /// </summary>
        public async Task<bool> UpdateDataProcedureAsync(int daysBack = 30)
        {
            var stats = await UpdateDataProcedureWithStatsAsync(daysBack);
            return stats.Success;
        }

        public void Dispose()
        {
            _databaseService?.Dispose();
            _yahooFinanceService?.Dispose();
        }
    }
}
