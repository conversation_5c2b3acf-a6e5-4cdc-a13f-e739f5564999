using System;
using System.Collections.Generic;
using System.Data;
using System.Net.Http;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using Newtonsoft.Json.Linq;
using System.Globalization;
using System.Text;

namespace EABDataUpdater
{
    /// <summary>
    /// EAB 0023.HK 数据更新器
    /// </summary>
    public class EAB0023HKDataUpdater
    {
        private readonly string _connectionString;
        private readonly string _symbol = "0023.HK";
        private readonly HttpClient _httpClient;

        public EAB0023HKDataUpdater(string connectionString)
        {
            _connectionString = connectionString;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        /// <summary>
        /// 数据更新事件
        /// </summary>
        public event EventHandler<string> OnStatusUpdate;
        public event EventHandler<string> OnError;

        /// <summary>
        /// 触发状态更新事件
        /// </summary>
        private void RaiseStatusUpdate(string message)
        {
            OnStatusUpdate?.Invoke(this, message);
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        private void RaiseError(string error)
        {
            OnError?.Invoke(this, error);
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    RaiseStatusUpdate("数据库连接成功");
                    return true;
                }
            }
            catch (Exception ex)
            {
                RaiseError($"数据库连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库中最新的日期
        /// </summary>
        public async Task<DateTime?> GetLatestDateFromDbAsync()
        {
            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    var query = "SELECT MAX(Date) FROM eab_0023hk";
                    
                    using (var command = new MySqlCommand(query, connection))
                    {
                        var result = await command.ExecuteScalarAsync();
                        if (result != null && result != DBNull.Value)
                        {
                            var latestDate = Convert.ToDateTime(result);
                            RaiseStatusUpdate($"数据库中最新日期: {latestDate:yyyy-MM-dd}");
                            return latestDate;
                        }
                        else
                        {
                            RaiseStatusUpdate("数据库中没有数据");
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                RaiseError($"获取最新日期失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从Yahoo Finance下载数据
        /// </summary>
        public async Task<List<StockData>> DownloadYahooFinanceDataAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                // 如果没有指定开始日期，默认下载最近30天的数据
                if (!startDate.HasValue)
                {
                    startDate = DateTime.Now.AddDays(-30);
                }

                if (!endDate.HasValue)
                {
                    endDate = DateTime.Now;
                }

                RaiseStatusUpdate($"正在下载 {_symbol} 从 {startDate:yyyy-MM-dd} 到 {endDate:yyyy-MM-dd} 的数据...");

                // 转换为Unix时间戳
                var startTimestamp = ((DateTimeOffset)startDate.Value).ToUnixTimeSeconds();
                var endTimestamp = ((DateTimeOffset)endDate.Value).ToUnixTimeSeconds();

                // 构建Yahoo Finance API URL
                var url = $"https://query1.finance.yahoo.com/v8/finance/chart/{_symbol}?period1={startTimestamp}&period2={endTimestamp}&interval=1d";

                var response = await _httpClient.GetStringAsync(url);
                var jsonData = JObject.Parse(response);

                var result = jsonData["chart"]["result"][0];
                var timestamps = result["timestamp"].ToObject<long[]>();
                var quotes = result["indicators"]["quote"][0];

                var opens = quotes["open"].ToObject<decimal?[]>();
                var highs = quotes["high"].ToObject<decimal?[]>();
                var lows = quotes["low"].ToObject<decimal?[]>();
                var closes = quotes["close"].ToObject<decimal?[]>();
                var volumes = quotes["volume"].ToObject<long?[]>();

                var stockDataList = new List<StockData>();

                for (int i = 0; i < timestamps.Length; i++)
                {
                    if (opens[i].HasValue && highs[i].HasValue && lows[i].HasValue && 
                        closes[i].HasValue && volumes[i].HasValue)
                    {
                        var date = DateTimeOffset.FromUnixTimeSeconds(timestamps[i]).DateTime;
                        
                        var stockData = new StockData
                        {
                            Date = date,
                            Open = opens[i].Value,
                            High = highs[i].Value,
                            Low = lows[i].Value,
                            Close = closes[i].Value,
                            Volume = volumes[i].Value
                        };

                        // 计算技术指标
                        CalculateTechnicalIndicators(stockData);
                        stockDataList.Add(stockData);
                    }
                }

                RaiseStatusUpdate($"成功下载 {stockDataList.Count} 条记录");
                return stockDataList;
            }
            catch (Exception ex)
            {
                RaiseError($"下载Yahoo Finance数据失败: {ex.Message}");
                return new List<StockData>();
            }
        }

        /// <summary>
        /// 计算技术指标
        /// </summary>
        private void CalculateTechnicalIndicators(StockData data)
        {
            // 计算TypicalPrice
            data.TypicalPrice = (data.High + data.Low + data.Close) / 3;
            
            // 计算MoneyFlow
            data.MoneyFlow = data.TypicalPrice * data.Volume;
        }

        /// <summary>
        /// 插入或更新数据到数据库
        /// </summary>
        public async Task<bool> InsertOrUpdateDataAsync(List<StockData> stockDataList)
        {
            if (stockDataList == null || stockDataList.Count == 0)
            {
                RaiseStatusUpdate("没有数据需要更新");
                return true;
            }

            try
            {
                using (var connection = new MySqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (var transaction = await connection.BeginTransactionAsync())
                    {
                        try
                        {
                            var insertQuery = @"
                                INSERT INTO eab_0023hk (
                                    Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
                                    PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
                                    Y_Value, X_Value, E_Value, RSI, TradingSignal, i, midprice,
                                    Controller, Full_Y, E
                                ) VALUES (
                                    @Date, @Open, @High, @Low, @Close, @Volume, @TypicalPrice, @MoneyFlow,
                                    @PositiveMoneyFlow, @NegativeMoneyFlow, @MoneyFlowRatio, @MFI,
                                    @Y_Value, @X_Value, @E_Value, @RSI, @TradingSignal, @i, @midprice,
                                    @Controller, @Full_Y, @E
                                ) ON DUPLICATE KEY UPDATE
                                    Open = VALUES(Open),
                                    High = VALUES(High),
                                    Low = VALUES(Low),
                                    Close = VALUES(Close),
                                    Volume = VALUES(Volume),
                                    TypicalPrice = VALUES(TypicalPrice),
                                    MoneyFlow = VALUES(MoneyFlow),
                                    updated_at = CURRENT_TIMESTAMP";

                            using (var command = new MySqlCommand(insertQuery, connection, transaction))
                            {
                                int updatedCount = 0;
                                
                                foreach (var stockData in stockDataList)
                                {
                                    command.Parameters.Clear();
                                    command.Parameters.AddWithValue("@Date", stockData.Date.Date);
                                    command.Parameters.AddWithValue("@Open", stockData.Open);
                                    command.Parameters.AddWithValue("@High", stockData.High);
                                    command.Parameters.AddWithValue("@Low", stockData.Low);
                                    command.Parameters.AddWithValue("@Close", stockData.Close);
                                    command.Parameters.AddWithValue("@Volume", stockData.Volume);
                                    command.Parameters.AddWithValue("@TypicalPrice", stockData.TypicalPrice);
                                    command.Parameters.AddWithValue("@MoneyFlow", stockData.MoneyFlow);
                                    command.Parameters.AddWithValue("@PositiveMoneyFlow", DBNull.Value);
                                    command.Parameters.AddWithValue("@NegativeMoneyFlow", DBNull.Value);
                                    command.Parameters.AddWithValue("@MoneyFlowRatio", DBNull.Value);
                                    command.Parameters.AddWithValue("@MFI", DBNull.Value);
                                    command.Parameters.AddWithValue("@Y_Value", DBNull.Value);
                                    command.Parameters.AddWithValue("@X_Value", DBNull.Value);
                                    command.Parameters.AddWithValue("@E_Value", DBNull.Value);
                                    command.Parameters.AddWithValue("@RSI", DBNull.Value);
                                    command.Parameters.AddWithValue("@TradingSignal", 0);
                                    command.Parameters.AddWithValue("@i", DBNull.Value);
                                    command.Parameters.AddWithValue("@midprice", DBNull.Value);
                                    command.Parameters.AddWithValue("@Controller", DBNull.Value);
                                    command.Parameters.AddWithValue("@Full_Y", DBNull.Value);
                                    command.Parameters.AddWithValue("@E", DBNull.Value);

                                    await command.ExecuteNonQueryAsync();
                                    updatedCount++;
                                }

                                await transaction.CommitAsync();
                                RaiseStatusUpdate($"成功插入/更新 {updatedCount} 条记录");
                                return true;
                            }
                        }
                        catch (Exception)
                        {
                            await transaction.RollbackAsync();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                RaiseError($"插入/更新数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 主要的数据更新存储过程
        /// </summary>
        public async Task<bool> UpdateDataProcedureAsync(int daysBack = 30)
        {
            try
            {
                RaiseStatusUpdate("开始数据更新过程...");

                // 测试数据库连接
                if (!await TestDatabaseConnectionAsync())
                {
                    return false;
                }

                // 获取数据库中最新日期
                var latestDate = await GetLatestDateFromDbAsync();

                // 确定下载的开始日期
                DateTime startDate;
                if (latestDate.HasValue)
                {
                    startDate = latestDate.Value.AddDays(1);
                }
                else
                {
                    startDate = DateTime.Now.AddDays(-daysBack);
                }

                // 下载数据
                var stockDataList = await DownloadYahooFinanceDataAsync(startDate);

                if (stockDataList.Count == 0)
                {
                    RaiseStatusUpdate("没有新数据需要更新");
                    return true;
                }

                // 插入/更新数据
                var success = await InsertOrUpdateDataAsync(stockDataList);

                if (success)
                {
                    RaiseStatusUpdate("数据更新完成");
                }

                return success;
            }
            catch (Exception ex)
            {
                RaiseError($"数据更新过程失败: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// 股票数据模型
    /// </summary>
    public class StockData
    {
        public DateTime Date { get; set; }
        public decimal Open { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Close { get; set; }
        public long Volume { get; set; }
        public decimal TypicalPrice { get; set; }
        public decimal MoneyFlow { get; set; }
    }
}
