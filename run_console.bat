@echo off
echo ========================================
echo EAB 0023.HK 数据更新器 - 控制台模式
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    pause
    exit /b 1
)

echo 正在编译项目...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 请输入数据库连接信息:
set /p server="MySQL服务器 (默认: localhost): "
if "%server%"=="" set server=localhost

set /p database="数据库名称 (默认: finance): "
if "%database%"=="" set database=finance

set /p username="用户名 (默认: root): "
if "%username%"=="" set username=root

set /p password="密码: "

set /p symbol="股票代码 (默认: 0023.HK): "
if "%symbol%"=="" set symbol=0023.HK

set /p days="回溯天数 (默认: 30): "
if "%days%"=="" set days=30

echo.
echo 开始更新数据...
echo 服务器: %server%
echo 数据库: %database%
echo 用户名: %username%
echo 股票代码: %symbol%
echo 回溯天数: %days%
echo.

dotnet run --configuration Release console --server %server% --database %database% --username %username% --password %password% --symbol %symbol% --days %days%

echo.
echo 按任意键退出...
pause >nul
