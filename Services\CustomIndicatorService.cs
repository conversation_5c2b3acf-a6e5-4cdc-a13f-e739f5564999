using System;
using System.Collections.Generic;
using System.Linq;
using EABDataUpdater.Models;

namespace EABDataUpdater.Services
{
    /// <summary>
    /// 自定义指标计算服务
    /// 用于计算 Full_Y, E, i, Controller, Y_Value, X_Value, E_Value 等自定义字段
    /// </summary>
    public class CustomIndicatorService
    {
        /// <summary>
        /// 计算所有自定义指标
        /// 注意：计算顺序很重要，因为有依赖关系
        /// </summary>
        public static void CalculateCustomIndicators(List<StockData> data)
        {
            if (data == null || data.Count == 0) return;

            var sortedData = data.OrderBy(x => x.Date).ToList();

            // 计算各种自定义指标 - 按依赖关系排序
            CalculateYValues(sortedData);        // 计算Y_Value
            CalculateXValues(sortedData);        // 计算X_Value
            CalculateEValues(sortedData);        // 计算E_Value
            CalculateController(sortedData);     // 计算Controller (需要先计算，因为Full_Y依赖它)
            CalculateFullY(sortedData);          // 计算Full_Y (依赖Controller)
            CalculateE(sortedData);              // 计算E (依赖MFI和Full_Y)
            CalculateI(sortedData);              // 计算i
            CalculateTradingSignal(sortedData);  // 计算交易信号
        }

        /// <summary>
        /// 计算 Y_Value - 基于价格变化的指标
        /// </summary>
        private static void CalculateYValues(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                var previous = data[i - 1];

                // Y_Value = (当前收盘价 - 前一日收盘价) / 前一日收盘价
                if (previous.Close > 0)
                {
                    current.Y_Value = (decimal)((double)(current.Close - previous.Close) / (double)previous.Close);
                }
                else
                {
                    current.Y_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 X_Value - 基于成交量变化的指标
        /// </summary>
        private static void CalculateXValues(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                var previous = data[i - 1];

                // X_Value = (当前成交量 - 前一日成交量) / 前一日成交量
                if (previous.Volume > 0)
                {
                    current.X_Value = (decimal)((double)(current.Volume - previous.Volume) / (double)previous.Volume);
                }
                else
                {
                    current.X_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 E_Value - 基于价格波动的指标
        /// </summary>
        private static void CalculateEValues(List<StockData> data)
        {
            for (int i = 0; i < data.Count; i++)
            {
                var current = data[i];

                // E_Value = (最高价 - 最低价) / 收盘价
                if (current.Close > 0)
                {
                    current.E_Value = (current.High - current.Low) / current.Close;
                }
                else
                {
                    current.E_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 Full_Y - Controller为1的总数/当前行数
        /// Full_Y = controller为1的总数/row，每行计算的
        /// </summary>
        private static void CalculateFullY(List<StockData> data)
        {
            int controllerCount = 0; // Controller为1的总数

            for (int i = 0; i < data.Count; i++)
            {
                // 统计到当前行为止，Controller为1的总数
                if (data[i].Controller.HasValue && data[i].Controller.Value == 1)
                {
                    controllerCount++;
                }

                // Full_Y = Controller为1的总数 / 当前行数
                int currentRow = i + 1; // 行数从1开始
                data[i].Full_Y = (decimal)controllerCount / currentRow;
            }
        }

        /// <summary>
        /// 计算 E - 能量指标
        /// E = (8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1
        /// </summary>
        private static void CalculateE(List<StockData> data)
        {
            for (int i = 0; i < data.Count; i++)
            {
                var current = data[i];

                // 确保MFI和Full_Y都有值
                if (current.MFI.HasValue && current.Full_Y.HasValue)
                {
                    decimal mfi = current.MFI.Value;
                    decimal fullY = current.Full_Y.Value;

                    // E = (8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1
                    decimal term1 = (8 * mfi / 100 - 3) * fullY;
                    decimal term2 = -3 * mfi / 100;
                    decimal term3 = 1;

                    current.E = term1 + term2 + term3;
                }
            }
        }

        /// <summary>
        /// 计算 i - 趋势指标
        /// i = 第一天记到现在的总天数（从第一条记录开始计算）
        /// </summary>
        private static void CalculateI(List<StockData> data)
        {
            for (int i = 0; i < data.Count; i++)
            {
                // i = 当前行数（从1开始计算）
                data[i].I = i + 1;
            }
        }

        /// <summary>
        /// 计算 Controller - 控制信号
        /// Controller = 1 当 Close Price > MidPrice，否则为0
        /// </summary>
        private static void CalculateController(List<StockData> data)
        {
            for (int i = 0; i < data.Count; i++)
            {
                var current = data[i];

                // Controller = 1 当 Close Price > MidPrice，否则为0
                if (current.MidPrice.HasValue)
                {
                    current.Controller = (byte)(current.Close > current.MidPrice.Value ? 1 : 0);
                }
                else
                {
                    // 如果MidPrice没有值，默认为0
                    current.Controller = 0;
                }
            }
        }

        /// <summary>
        /// 计算交易信号
        /// </summary>
        private static void CalculateTradingSignal(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                int signal = 0;

                // 买入信号条件
                bool buyCondition1 = current.RSI.HasValue && current.RSI.Value < 30; // RSI超卖
                bool buyCondition2 = current.MFI.HasValue && current.MFI.Value < 20; // MFI超卖
                bool buyCondition3 = current.Y_Value.HasValue && current.Y_Value.Value < -0.03m; // 价格下跌3%以上
                bool buyCondition4 = current.I.HasValue && current.I.Value < -5; // 趋势指标偏空

                // 卖出信号条件
                bool sellCondition1 = current.RSI.HasValue && current.RSI.Value > 70; // RSI超买
                bool sellCondition2 = current.MFI.HasValue && current.MFI.Value > 80; // MFI超买
                bool sellCondition3 = current.Y_Value.HasValue && current.Y_Value.Value > 0.03m; // 价格上涨3%以上
                bool sellCondition4 = current.I.HasValue && current.I.Value > 5; // 趋势指标偏多

                // 强买入信号 (多个条件同时满足)
                if ((buyCondition1 && buyCondition2) || (buyCondition1 && buyCondition3) || (buyCondition2 && buyCondition4))
                {
                    signal = 2; // 强买入
                }
                // 普通买入信号
                else if (buyCondition1 || buyCondition2 || (buyCondition3 && buyCondition4))
                {
                    signal = 1; // 买入
                }
                // 强卖出信号
                else if ((sellCondition1 && sellCondition2) || (sellCondition1 && sellCondition3) || (sellCondition2 && sellCondition4))
                {
                    signal = -2; // 强卖出
                }
                // 普通卖出信号
                else if (sellCondition1 || sellCondition2 || (sellCondition3 && sellCondition4))
                {
                    signal = -1; // 卖出
                }

                current.TradingSignal = signal;
            }
        }

        /// <summary>
        /// 获取指标说明
        /// </summary>
        public static Dictionary<string, string> GetIndicatorDescriptions()
        {
            return new Dictionary<string, string>
            {
                { "Y_Value", "价格变化率：(当前收盘价 - 前一日收盘价) / 前一日收盘价" },
                { "X_Value", "成交量变化率：(当前成交量 - 前一日成交量) / 前一日成交量" },
                { "E_Value", "价格波动率：(最高价 - 最低价) / 收盘价" },
                { "Full_Y", "累积比例指标：Controller为1的总数 / 当前行数" },
                { "E", "能量指标：(8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1" },
                { "i", "序号指标：从第一天记到现在的总天数（当前行数）" },
                { "Controller", "价格位置信号：1当Close>MidPrice，否则为0" },
                { "TradingSignal", "交易信号：2=强买入, 1=买入, 0=持有, -1=卖出, -2=强卖出" }
            };
        }
    }
}
