using System;
using System.Collections.Generic;
using System.Linq;
using EABDataUpdater.Models;

namespace EABDataUpdater.Services
{
    /// <summary>
    /// 自定义指标计算服务
    /// 用于计算 Full_Y, E, i, Controller, Y_Value, X_Value, E_Value 等自定义字段
    /// </summary>
    public class CustomIndicatorService
    {
        /// <summary>
        /// 计算所有自定义指标
        /// </summary>
        public static void CalculateCustomIndicators(List<StockData> data)
        {
            if (data == null || data.Count == 0) return;

            var sortedData = data.OrderBy(x => x.Date).ToList();
            
            // 计算各种自定义指标
            CalculateYValues(sortedData);
            CalculateXValues(sortedData);
            CalculateEValues(sortedData);
            CalculateFullY(sortedData);
            CalculateE(sortedData);
            CalculateI(sortedData);
            CalculateController(sortedData);
            CalculateTradingSignal(sortedData);
        }

        /// <summary>
        /// 计算 Y_Value - 基于价格变化的指标
        /// </summary>
        private static void CalculateYValues(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                var previous = data[i - 1];
                
                // Y_Value = (当前收盘价 - 前一日收盘价) / 前一日收盘价
                if (previous.Close > 0)
                {
                    current.Y_Value = (decimal)((double)(current.Close - previous.Close) / (double)previous.Close);
                }
                else
                {
                    current.Y_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 X_Value - 基于成交量变化的指标
        /// </summary>
        private static void CalculateXValues(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                var previous = data[i - 1];
                
                // X_Value = (当前成交量 - 前一日成交量) / 前一日成交量
                if (previous.Volume > 0)
                {
                    current.X_Value = (decimal)((double)(current.Volume - previous.Volume) / (double)previous.Volume);
                }
                else
                {
                    current.X_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 E_Value - 基于价格波动的指标
        /// </summary>
        private static void CalculateEValues(List<StockData> data)
        {
            for (int i = 0; i < data.Count; i++)
            {
                var current = data[i];
                
                // E_Value = (最高价 - 最低价) / 收盘价
                if (current.Close > 0)
                {
                    current.E_Value = (current.High - current.Low) / current.Close;
                }
                else
                {
                    current.E_Value = 0;
                }
            }
        }

        /// <summary>
        /// 计算 Full_Y - 基于多日价格变化的综合指标
        /// </summary>
        private static void CalculateFullY(List<StockData> data, int period = 5)
        {
            for (int i = period; i < data.Count; i++)
            {
                decimal sum = 0;
                int count = 0;
                
                // 计算过去N天的Y_Value平均值
                for (int j = i - period + 1; j <= i; j++)
                {
                    if (data[j].Y_Value.HasValue)
                    {
                        sum += data[j].Y_Value.Value;
                        count++;
                    }
                }
                
                if (count > 0)
                {
                    data[i].Full_Y = sum / count;
                }
            }
        }

        /// <summary>
        /// 计算 E - 基于能量的指标
        /// </summary>
        private static void CalculateE(List<StockData> data, int period = 10)
        {
            for (int i = period; i < data.Count; i++)
            {
                decimal priceEnergy = 0;
                decimal volumeEnergy = 0;
                
                // 计算价格能量和成交量能量
                for (int j = i - period + 1; j <= i; j++)
                {
                    var current = data[j];
                    
                    // 价格能量 = 价格变化幅度 * 成交量
                    if (current.E_Value.HasValue)
                    {
                        priceEnergy += current.E_Value.Value * current.Volume;
                    }
                    
                    // 成交量能量
                    volumeEnergy += current.Volume;
                }
                
                // E = 价格能量 / 成交量能量
                if (volumeEnergy > 0)
                {
                    data[i].E = priceEnergy / volumeEnergy;
                }
            }
        }

        /// <summary>
        /// 计算 i - 基于趋势的指标
        /// </summary>
        private static void CalculateI(List<StockData> data, int period = 20)
        {
            for (int i = period; i < data.Count; i++)
            {
                int upDays = 0;
                int downDays = 0;
                
                // 统计上涨和下跌天数
                for (int j = i - period + 1; j <= i; j++)
                {
                    if (j > 0)
                    {
                        if (data[j].Close > data[j - 1].Close)
                            upDays++;
                        else if (data[j].Close < data[j - 1].Close)
                            downDays++;
                    }
                }
                
                // i = 上涨天数 - 下跌天数
                data[i].I = upDays - downDays;
            }
        }

        /// <summary>
        /// 计算 Controller - 控制信号
        /// </summary>
        private static void CalculateController(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                
                byte controller = 0;
                
                // 基于多个条件判断控制信号
                // 条件1: RSI超买超卖
                if (current.RSI.HasValue)
                {
                    if (current.RSI.Value > 70)
                        controller |= 1; // 超买
                    else if (current.RSI.Value < 30)
                        controller |= 2; // 超卖
                }
                
                // 条件2: MFI资金流
                if (current.MFI.HasValue)
                {
                    if (current.MFI.Value > 80)
                        controller |= 4; // 资金流出
                    else if (current.MFI.Value < 20)
                        controller |= 8; // 资金流入
                }
                
                // 条件3: 价格趋势
                if (current.Y_Value.HasValue)
                {
                    if (current.Y_Value.Value > 0.05m) // 上涨超过5%
                        controller |= 16;
                    else if (current.Y_Value.Value < -0.05m) // 下跌超过5%
                        controller |= 32;
                }
                
                // 条件4: 成交量异常
                if (current.X_Value.HasValue)
                {
                    if (current.X_Value.Value > 1.0m) // 成交量翻倍
                        controller |= 64;
                }
                
                current.Controller = controller;
            }
        }

        /// <summary>
        /// 计算交易信号
        /// </summary>
        private static void CalculateTradingSignal(List<StockData> data)
        {
            for (int i = 1; i < data.Count; i++)
            {
                var current = data[i];
                int signal = 0;
                
                // 买入信号条件
                bool buyCondition1 = current.RSI.HasValue && current.RSI.Value < 30; // RSI超卖
                bool buyCondition2 = current.MFI.HasValue && current.MFI.Value < 20; // MFI超卖
                bool buyCondition3 = current.Y_Value.HasValue && current.Y_Value.Value < -0.03m; // 价格下跌3%以上
                bool buyCondition4 = current.I.HasValue && current.I.Value < -5; // 趋势指标偏空
                
                // 卖出信号条件
                bool sellCondition1 = current.RSI.HasValue && current.RSI.Value > 70; // RSI超买
                bool sellCondition2 = current.MFI.HasValue && current.MFI.Value > 80; // MFI超买
                bool sellCondition3 = current.Y_Value.HasValue && current.Y_Value.Value > 0.03m; // 价格上涨3%以上
                bool sellCondition4 = current.I.HasValue && current.I.Value > 5; // 趋势指标偏多
                
                // 强买入信号 (多个条件同时满足)
                if ((buyCondition1 && buyCondition2) || (buyCondition1 && buyCondition3) || (buyCondition2 && buyCondition4))
                {
                    signal = 2; // 强买入
                }
                // 普通买入信号
                else if (buyCondition1 || buyCondition2 || (buyCondition3 && buyCondition4))
                {
                    signal = 1; // 买入
                }
                // 强卖出信号
                else if ((sellCondition1 && sellCondition2) || (sellCondition1 && sellCondition3) || (sellCondition2 && sellCondition4))
                {
                    signal = -2; // 强卖出
                }
                // 普通卖出信号
                else if (sellCondition1 || sellCondition2 || (sellCondition3 && sellCondition4))
                {
                    signal = -1; // 卖出
                }
                
                current.TradingSignal = signal;
            }
        }

        /// <summary>
        /// 获取指标说明
        /// </summary>
        public static Dictionary<string, string> GetIndicatorDescriptions()
        {
            return new Dictionary<string, string>
            {
                { "Y_Value", "价格变化率：(当前收盘价 - 前一日收盘价) / 前一日收盘价" },
                { "X_Value", "成交量变化率：(当前成交量 - 前一日成交量) / 前一日成交量" },
                { "E_Value", "价格波动率：(最高价 - 最低价) / 收盘价" },
                { "Full_Y", "多日价格变化平均值：过去5天Y_Value的平均值" },
                { "E", "能量指标：价格能量与成交量能量的比值" },
                { "i", "趋势指标：过去20天上涨天数减去下跌天数" },
                { "Controller", "控制信号：基于RSI、MFI、价格趋势、成交量的综合信号" },
                { "TradingSignal", "交易信号：2=强买入, 1=买入, 0=持有, -1=卖出, -2=强卖出" }
            };
        }
    }
}
