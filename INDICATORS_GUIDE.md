# EAB 0023.HK 自定义指标说明

本文档详细说明了系统中所有自定义指标的计算方法和含义。

## 📊 基础数据字段

### Yahoo Finance 原始数据
- **Date**: 交易日期
- **Open**: 开盘价
- **High**: 最高价  
- **Low**: 最低价
- **Close**: 收盘价
- **Volume**: 成交量

### 基本计算指标
- **TypicalPrice**: 典型价格 = (High + Low + Close) / 3
- **MoneyFlow**: 资金流 = TypicalPrice × Volume
- **MidPrice**: 中间价格 = (High + Low) / 2

## 🔢 自定义指标详解

### 1. Y_Value (价格变化率)
```
计算公式: Y_Value = (当前收盘价 - 前一日收盘价) / 前一日收盘价
```
- **含义**: 反映股价的日变化率
- **范围**: -1.0 到 +∞ (通常在 -0.1 到 +0.1 之间)
- **解读**: 
  - 正值表示上涨
  - 负值表示下跌
  - 绝对值越大，变化幅度越大

### 2. X_Value (成交量变化率)
```
计算公式: X_Value = (当前成交量 - 前一日成交量) / 前一日成交量
```
- **含义**: 反映成交量的变化情况
- **范围**: -1.0 到 +∞
- **解读**:
  - 正值表示成交量增加
  - 负值表示成交量减少
  - 大于1.0表示成交量翻倍以上

### 3. E_Value (价格波动率)
```
计算公式: E_Value = (最高价 - 最低价) / 收盘价
```
- **含义**: 反映当日价格波动的剧烈程度
- **范围**: 0 到 +∞ (通常在 0.01 到 0.1 之间)
- **解读**:
  - 值越大，当日波动越剧烈
  - 值越小，价格相对稳定

### 4. Full_Y (多日价格变化平均值)
```
计算公式: Full_Y = 过去5天Y_Value的平均值
```
- **含义**: 反映短期价格趋势
- **范围**: 类似Y_Value，但更平滑
- **解读**:
  - 正值表示短期上涨趋势
  - 负值表示短期下跌趋势
  - 比Y_Value更稳定，减少噪音

### 5. E (能量指标)
```
计算公式: E = Σ(E_Value × Volume) / Σ(Volume) (过去10天)
```
- **含义**: 反映价格波动与成交量的综合能量
- **范围**: 0 到 +∞
- **解读**:
  - 值越大，市场活跃度越高
  - 结合了价格波动和成交量信息

### 6. i (趋势指标)
```
计算公式: i = 过去20天上涨天数 - 过去20天下跌天数
```
- **含义**: 反映中期趋势强度
- **范围**: -20 到 +20
- **解读**:
  - 正值表示上涨趋势
  - 负值表示下跌趋势
  - 绝对值越大，趋势越明显

### 7. Controller (控制信号)
```
计算方法: 基于多个条件的位运算组合
- 位1 (1): RSI > 70 (超买)
- 位2 (2): RSI < 30 (超卖)  
- 位3 (4): MFI > 80 (资金流出)
- 位4 (8): MFI < 20 (资金流入)
- 位5 (16): Y_Value > 5% (大涨)
- 位6 (32): Y_Value < -5% (大跌)
- 位7 (64): X_Value > 100% (成交量翻倍)
```
- **含义**: 综合市场状态的控制信号
- **范围**: 0 到 127 (7位二进制)
- **解读**: 每一位代表一个市场条件，可以组合分析

### 8. TradingSignal (交易信号)
```
信号值含义:
- +2: 强买入信号
- +1: 买入信号
- 0: 持有/观望
- -1: 卖出信号
- -2: 强卖出信号
```
- **计算逻辑**:
  - **买入条件**: RSI超卖 + MFI超卖 + 价格大跌 + 趋势偏空
  - **卖出条件**: RSI超买 + MFI超买 + 价格大涨 + 趋势偏多
  - **强信号**: 多个条件同时满足
  - **普通信号**: 部分条件满足

## 🎯 技术指标 (标准)

### RSI (相对强弱指数)
- **周期**: 14天
- **范围**: 0-100
- **超买线**: 70
- **超卖线**: 30

### MFI (资金流量指数)
- **周期**: 14天
- **范围**: 0-100
- **超买线**: 80
- **超卖线**: 20

## 📈 使用建议

### 1. 趋势分析
- 结合 `Y_Value`, `Full_Y`, `i` 判断趋势方向
- `i` > 5 且 `Full_Y` > 0: 强上涨趋势
- `i` < -5 且 `Full_Y` < 0: 强下跌趋势

### 2. 波动性分析
- 使用 `E_Value` 和 `E` 判断市场波动
- 高波动期间需要更谨慎的交易策略

### 3. 成交量分析
- `X_Value` 配合价格变化分析
- 价格上涨 + 成交量放大 = 强势信号
- 价格下跌 + 成交量萎缩 = 弱势信号

### 4. 综合信号
- `TradingSignal` 提供直接的交易建议
- `Controller` 提供详细的市场状态信息
- 建议结合多个指标综合判断

## ⚠️ 注意事项

1. **数据质量**: 确保Yahoo Finance数据的准确性
2. **计算顺序**: 某些指标依赖于前期数据，需要按时间顺序计算
3. **参数调整**: 可以根据市场特性调整计算周期
4. **风险控制**: 指标仅供参考，不构成投资建议
5. **回测验证**: 建议对指标进行历史回测验证

## 🔧 自定义修改

如需修改指标计算逻辑，请编辑 `Services/CustomIndicatorService.cs` 文件中的相应方法：

- `CalculateYValues()`: 修改Y_Value计算
- `CalculateXValues()`: 修改X_Value计算  
- `CalculateEValues()`: 修改E_Value计算
- `CalculateFullY()`: 修改Full_Y计算
- `CalculateE()`: 修改E计算
- `CalculateI()`: 修改i计算
- `CalculateController()`: 修改Controller计算
- `CalculateTradingSignal()`: 修改交易信号计算

修改后需要重新编译程序并重新计算历史数据。
