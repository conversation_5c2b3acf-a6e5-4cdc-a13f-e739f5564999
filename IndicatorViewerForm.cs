using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using EABDataUpdater.Services;

namespace EABDataUpdater
{
    /// <summary>
    /// 指标查看器窗体
    /// </summary>
    public partial class IndicatorViewerForm : Form
    {
        private DataGridView dataGridView;
        private Button btnLoadData;
        private Button btnRefresh;
        private Label lblStatus;
        private ComboBox cmbDays;
        private GroupBox groupBoxConfig;
        private TextBox txtServer;
        private TextBox txtDatabase;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private DatabaseService _databaseService;

        public IndicatorViewerForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 主窗体设置
            this.Text = "EAB 0023.HK 指标查看器";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);

            // 配置组框
            groupBoxConfig = new GroupBox
            {
                Text = "数据库配置",
                Location = new Point(12, 12),
                Size = new Size(600, 80),
                Anchor = AnchorStyles.Top | AnchorStyles.Left
            };

            // 服务器
            var lblServer = new Label { Text = "服务器:", Location = new Point(15, 25), Size = new Size(50, 23) };
            txtServer = new TextBox { Text = "localhost", Location = new Point(70, 22), Size = new Size(100, 23) };

            // 数据库
            var lblDatabase = new Label { Text = "数据库:", Location = new Point(180, 25), Size = new Size(50, 23) };
            txtDatabase = new TextBox { Text = "finance", Location = new Point(235, 22), Size = new Size(100, 23) };

            // 用户名
            var lblUsername = new Label { Text = "用户名:", Location = new Point(15, 50), Size = new Size(50, 23) };
            txtUsername = new TextBox { Text = "root", Location = new Point(70, 47), Size = new Size(100, 23) };

            // 密码
            var lblPassword = new Label { Text = "密码:", Location = new Point(180, 50), Size = new Size(50, 23) };
            txtPassword = new TextBox { UseSystemPasswordChar = true, Location = new Point(235, 47), Size = new Size(100, 23) };

            groupBoxConfig.Controls.AddRange(new Control[] 
            { 
                lblServer, txtServer, lblDatabase, txtDatabase, 
                lblUsername, txtUsername, lblPassword, txtPassword
            });

            // 控制按钮
            btnLoadData = new Button
            {
                Text = "加载数据",
                Location = new Point(630, 25),
                Size = new Size(80, 30),
                BackColor = Color.LightBlue
            };
            btnLoadData.Click += BtnLoadData_Click;

            btnRefresh = new Button
            {
                Text = "刷新",
                Location = new Point(720, 25),
                Size = new Size(60, 30),
                BackColor = Color.LightGreen
            };
            btnRefresh.Click += BtnRefresh_Click;

            // 天数选择
            var lblDays = new Label { Text = "显示天数:", Location = new Point(630, 60), Size = new Size(60, 23) };
            cmbDays = new ComboBox 
            { 
                Location = new Point(695, 57), 
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbDays.Items.AddRange(new object[] { "10", "30", "60", "90", "180", "365", "全部" });
            cmbDays.SelectedIndex = 1; // 默认30天

            // 状态标签
            lblStatus = new Label
            {
                Text = "就绪",
                Location = new Point(12, 105),
                Size = new Size(600, 23),
                ForeColor = Color.Blue
            };

            // 数据表格
            dataGridView = new DataGridView
            {
                Location = new Point(12, 135),
                Size = new Size(1160, 620),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.DisplayedCells,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[] 
            { 
                groupBoxConfig, btnLoadData, btnRefresh, lblDays, cmbDays, lblStatus, dataGridView 
            });

            this.ResumeLayout(false);
        }

        private void SetStatus(string message, Color color)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, Color>(SetStatus), message, color);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = color;
        }

        private string GetConnectionString()
        {
            return $"Server={txtServer.Text};Database={txtDatabase.Text};Uid={txtUsername.Text};Pwd={txtPassword.Text};CharSet=utf8mb4;SslMode=None;";
        }

        private async void BtnLoadData_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                btnLoadData.Enabled = false;
                btnRefresh.Enabled = false;
                SetStatus("正在加载数据...", Color.Blue);

                var connectionString = GetConnectionString();
                _databaseService?.Dispose();
                _databaseService = new DatabaseService(connectionString);

                // 构建查询
                var daysText = cmbDays.SelectedItem?.ToString() ?? "30";
                string whereClause = "";
                
                if (daysText != "全部" && int.TryParse(daysText, out int days))
                {
                    whereClause = $"WHERE Date >= DATE_SUB(CURDATE(), INTERVAL {days} DAY)";
                }

                var query = $@"
                    SELECT 
                        Date,
                        Open,
                        High,
                        Low,
                        Close,
                        Volume,
                        TypicalPrice,
                        MoneyFlow,
                        PositiveMoneyFlow,
                        NegativeMoneyFlow,
                        MoneyFlowRatio,
                        MFI,
                        Y_Value,
                        X_Value,
                        E_Value,
                        RSI,
                        TradingSignal,
                        i,
                        midprice,
                        Controller,
                        Full_Y,
                        E,
                        created_at,
                        updated_at
                    FROM eab_0023hk 
                    {whereClause}
                    ORDER BY Date DESC
                    LIMIT 1000";

                // 执行查询并绑定到DataGridView
                await _databaseService.ConnectAsync();
                using (var connection = new MySql.Data.MySqlClient.MySqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var adapter = new MySql.Data.MySqlClient.MySqlDataAdapter(query, connection))
                    {
                        var dataTable = new System.Data.DataTable();
                        adapter.Fill(dataTable);
                        
                        dataGridView.DataSource = dataTable;
                        
                        // 设置列格式
                        FormatColumns();
                        
                        SetStatus($"成功加载 {dataTable.Rows.Count} 条记录", Color.Green);
                    }
                }
            }
            catch (Exception ex)
            {
                SetStatus($"加载数据失败: {ex.Message}", Color.Red);
                MessageBox.Show($"加载数据失败:\n{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnLoadData.Enabled = true;
                btnRefresh.Enabled = true;
            }
        }

        private void FormatColumns()
        {
            if (dataGridView.DataSource == null) return;

            // 设置列宽和格式
            foreach (DataGridViewColumn column in dataGridView.Columns)
            {
                switch (column.Name.ToLower())
                {
                    case "date":
                        column.HeaderText = "日期";
                        column.DefaultCellStyle.Format = "yyyy-MM-dd";
                        column.Width = 100;
                        break;
                    case "open":
                        column.HeaderText = "开盘价";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 80;
                        break;
                    case "high":
                        column.HeaderText = "最高价";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 80;
                        break;
                    case "low":
                        column.HeaderText = "最低价";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 80;
                        break;
                    case "close":
                        column.HeaderText = "收盘价";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 80;
                        break;
                    case "volume":
                        column.HeaderText = "成交量";
                        column.DefaultCellStyle.Format = "N0";
                        column.Width = 100;
                        break;
                    case "typicalprice":
                        column.HeaderText = "典型价格";
                        column.DefaultCellStyle.Format = "F4";
                        column.Width = 90;
                        break;
                    case "moneyflow":
                        column.HeaderText = "资金流";
                        column.DefaultCellStyle.Format = "N0";
                        column.Width = 100;
                        break;
                    case "mfi":
                        column.HeaderText = "MFI";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 60;
                        break;
                    case "rsi":
                        column.HeaderText = "RSI";
                        column.DefaultCellStyle.Format = "F2";
                        column.Width = 60;
                        break;
                    case "y_value":
                        column.HeaderText = "Y值";
                        column.DefaultCellStyle.Format = "F6";
                        column.Width = 80;
                        break;
                    case "x_value":
                        column.HeaderText = "X值";
                        column.DefaultCellStyle.Format = "F6";
                        column.Width = 80;
                        break;
                    case "e_value":
                        column.HeaderText = "E值";
                        column.DefaultCellStyle.Format = "F4";
                        column.Width = 80;
                        break;
                    case "full_y":
                        column.HeaderText = "Full_Y";
                        column.DefaultCellStyle.Format = "F6";
                        column.Width = 80;
                        break;
                    case "e":
                        column.HeaderText = "E";
                        column.DefaultCellStyle.Format = "F6";
                        column.Width = 80;
                        break;
                    case "i":
                        column.HeaderText = "i";
                        column.Width = 50;
                        break;
                    case "controller":
                        column.HeaderText = "控制器";
                        column.Width = 60;
                        break;
                    case "tradingsignal":
                        column.HeaderText = "交易信号";
                        column.Width = 80;
                        // 根据信号值设置颜色
                        column.DefaultCellStyle.BackColor = Color.White;
                        break;
                    case "midprice":
                        column.HeaderText = "中间价";
                        column.DefaultCellStyle.Format = "F4";
                        column.Width = 80;
                        break;
                    case "created_at":
                        column.HeaderText = "创建时间";
                        column.DefaultCellStyle.Format = "yyyy-MM-dd HH:mm:ss";
                        column.Width = 150;
                        break;
                    case "updated_at":
                        column.HeaderText = "更新时间";
                        column.DefaultCellStyle.Format = "yyyy-MM-dd HH:mm:ss";
                        column.Width = 150;
                        break;
                }
            }

            // 设置交易信号的颜色
            dataGridView.CellFormatting += (s, e) =>
            {
                if (e.ColumnIndex >= 0 && dataGridView.Columns[e.ColumnIndex].Name.ToLower() == "tradingsignal")
                {
                    if (e.Value != null && int.TryParse(e.Value.ToString(), out int signal))
                    {
                        switch (signal)
                        {
                            case 2:
                                e.CellStyle.BackColor = Color.DarkGreen;
                                e.CellStyle.ForeColor = Color.White;
                                break;
                            case 1:
                                e.CellStyle.BackColor = Color.LightGreen;
                                break;
                            case -1:
                                e.CellStyle.BackColor = Color.LightCoral;
                                break;
                            case -2:
                                e.CellStyle.BackColor = Color.DarkRed;
                                e.CellStyle.ForeColor = Color.White;
                                break;
                            default:
                                e.CellStyle.BackColor = Color.White;
                                break;
                        }
                    }
                }
            };
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _databaseService?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
