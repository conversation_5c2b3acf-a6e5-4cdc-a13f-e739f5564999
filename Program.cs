using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EABDataUpdater
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static async Task<int> Main(string[] args)
        {
            // 检查命令行参数
            if (args.Length > 0)
            {
                var mode = args[0].ToLower();

                switch (mode)
                {
                    case "console":
                        // 控制台模式
                        return await ConsoleApp.RunFromArgsAsync(args);

                    case "test":
                        // 测试窗体模式
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        Application.Run(new TestForm());
                        return 0;

                    case "help":
                    case "--help":
                    case "-h":
                        ShowUsage();
                        return 0;

                    default:
                        Console.WriteLine($"未知模式: {mode}");
                        ShowUsage();
                        return 1;
                }
            }
            else
            {
                // 默认GUI模式
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new MainForm());
                return 0;
            }
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private static void ShowUsage()
        {
            Console.WriteLine("EAB 0023.HK 数据更新器");
            Console.WriteLine();
            Console.WriteLine("使用方法:");
            Console.WriteLine("  EABDataUpdater.exe                    启动GUI界面");
            Console.WriteLine("  EABDataUpdater.exe test               启动测试界面");
            Console.WriteLine("  EABDataUpdater.exe console [选项]     控制台模式");
            Console.WriteLine("  EABDataUpdater.exe help               显示帮助");
            Console.WriteLine();
            Console.WriteLine("控制台模式选项:");
            Console.WriteLine("  --server <服务器>     MySQL服务器地址 (默认: localhost)");
            Console.WriteLine("  --database <数据库>   数据库名称 (默认: finance)");
            Console.WriteLine("  --username <用户名>   数据库用户名 (默认: root)");
            Console.WriteLine("  --password <密码>     数据库密码 (默认: 空)");
            Console.WriteLine("  --symbol <股票代码>   股票代码 (默认: 0023.HK)");
            Console.WriteLine("  --days <天数>         回溯天数 (默认: 30)");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("  EABDataUpdater.exe console --username root --password mypass");
            Console.WriteLine("  EABDataUpdater.exe console --days 60 --symbol 0023.HK");
        }
    }
}
