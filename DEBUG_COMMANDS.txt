EAB 0023.HK Debug Commands
===========================

To diagnose why the force update is not working, run these commands:

1. BUILD PROJECT:
   dotnet build --configuration Release

2. RUN DEBUG MODE (replace 'yourpassword' with your actual password):
   dotnet run --configuration Release console --debug --server localhost --database finance --username root --password yourpassword --symbol 0023.HK --days 7

3. ALTERNATIVE - USE BATCH FILE:
   debug_update.bat

WHAT DEBUG MODE WILL SHOW:
- Database connection status
- Table existence and record count
- Current latest date in database
- Yahoo Finance connection status
- Stock information retrieval
- Data download attempt with date range
- Sample downloaded data (if any)
- Indicator calculation results
- Database insert attempt

COMMON ISSUES TO CHECK:
1. Date Range Problem:
   - Yahoo Finance might not have data for recent dates
   - Weekend/holiday dates have no trading data
   - Time zone differences

2. Database Issues:
   - Connection problems
   - Table structure mismatch
   - Permission issues

3. Network Issues:
   - Yahoo Finance API blocked
   - Firewall restrictions
   - Internet connectivity

4. Data Processing Issues:
   - Invalid data format
   - Calculation errors
   - Null value handling

AFTER RUNNING DEBUG:
Look for these key indicators:
- "Downloaded records: X" (should be > 0)
- "Insert success: True" (should be True)
- Any ERROR messages in the output

If you see "Downloaded records: 0", the issue is with data download.
If you see "Insert success: False", the issue is with database insertion.
