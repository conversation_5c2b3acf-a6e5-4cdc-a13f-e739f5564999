using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;

namespace EABDataUpdater.Services
{
    /// <summary>
    /// 数据库服务类
    /// </summary>
    public class DatabaseService : IDisposable
    {
        private readonly string _connectionString;
        private MySqlConnection _connection;

        public DatabaseService(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 数据库操作事件
        /// </summary>
        public event EventHandler<string> OnStatusUpdate;
        public event EventHandler<string> OnError;

        /// <summary>
        /// 触发状态更新事件
        /// </summary>
        protected virtual void RaiseStatusUpdate(string message)
        {
            OnStatusUpdate?.Invoke(this, message);
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        protected virtual void RaiseError(string error)
        {
            OnError?.Invoke(this, error);
        }

        /// <summary>
        /// 连接数据库
        /// </summary>
        public async Task<bool> ConnectAsync()
        {
            try
            {
                if (_connection?.State == ConnectionState.Open)
                {
                    return true;
                }

                _connection = new MySqlConnection(_connectionString);
                await _connection.OpenAsync();
                
                RaiseStatusUpdate("数据库连接成功");
                return true;
            }
            catch (Exception ex)
            {
                RaiseError($"数据库连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using (var testConnection = new MySqlConnection(_connectionString))
                {
                    await testConnection.OpenAsync();
                    
                    using (var command = new MySqlCommand("SELECT VERSION()", testConnection))
                    {
                        var version = await command.ExecuteScalarAsync();
                        RaiseStatusUpdate($"数据库连接测试成功，版本: {version}");
                    }
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                RaiseError($"数据库连接测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        public async Task<bool> CheckTableExistsAsync(string tableName)
        {
            try
            {
                if (!await ConnectAsync()) return false;

                var query = "SHOW TABLES LIKE @tableName";
                using (var command = new MySqlCommand(query, _connection))
                {
                    command.Parameters.AddWithValue("@tableName", tableName);
                    var result = await command.ExecuteScalarAsync();
                    
                    bool exists = result != null;
                    RaiseStatusUpdate($"表 {tableName} {(exists ? "存在" : "不存在")}");
                    return exists;
                }
            }
            catch (Exception ex)
            {
                RaiseError($"检查表存在性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取表记录数
        /// </summary>
        public async Task<long> GetTableRecordCountAsync(string tableName)
        {
            try
            {
                if (!await ConnectAsync()) return -1;

                var query = $"SELECT COUNT(*) FROM {tableName}";
                using (var command = new MySqlCommand(query, _connection))
                {
                    var result = await command.ExecuteScalarAsync();
                    long count = Convert.ToInt64(result);
                    RaiseStatusUpdate($"表 {tableName} 当前记录数: {count}");
                    return count;
                }
            }
            catch (Exception ex)
            {
                RaiseError($"获取表记录数失败: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取最新日期
        /// </summary>
        public async Task<DateTime?> GetLatestDateAsync(string tableName, string dateColumn = "Date")
        {
            try
            {
                if (!await ConnectAsync()) return null;

                var query = $"SELECT MAX({dateColumn}) FROM {tableName}";
                using (var command = new MySqlCommand(query, _connection))
                {
                    var result = await command.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        var latestDate = Convert.ToDateTime(result);
                        RaiseStatusUpdate($"数据库中最新日期: {latestDate:yyyy-MM-dd}");
                        return latestDate;
                    }
                    else
                    {
                        RaiseStatusUpdate("数据库中没有数据");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                RaiseError($"获取最新日期失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取表结构信息
        /// </summary>
        public async Task<List<ColumnInfo>> GetTableStructureAsync(string tableName)
        {
            var columns = new List<ColumnInfo>();
            
            try
            {
                if (!await ConnectAsync()) return columns;

                var query = $"DESCRIBE {tableName}";
                using (var command = new MySqlCommand(query, _connection))
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        columns.Add(new ColumnInfo
                        {
                            Name = reader.GetString("Field"),
                            Type = reader.GetString("Type"),
                            Null = reader.GetString("Null"),
                            Key = reader.IsDBNull("Key") ? "" : reader.GetString("Key"),
                            Default = reader.IsDBNull("Default") ? null : reader.GetString("Default"),
                            Extra = reader.IsDBNull("Extra") ? "" : reader.GetString("Extra")
                        });
                    }
                }

                RaiseStatusUpdate($"获取表 {tableName} 结构信息成功，共 {columns.Count} 个字段");
                return columns;
            }
            catch (Exception ex)
            {
                RaiseError($"获取表结构失败: {ex.Message}");
                return columns;
            }
        }

        /// <summary>
        /// 执行批量插入/更新操作
        /// </summary>
        public async Task<bool> BulkInsertOrUpdateAsync<T>(string tableName, List<T> data, 
            Func<T, Dictionary<string, object>> mapToParameters, string insertQuery)
        {
            if (data == null || data.Count == 0)
            {
                RaiseStatusUpdate("没有数据需要插入/更新");
                return true;
            }

            try
            {
                if (!await ConnectAsync()) return false;

                using (var transaction = await _connection.BeginTransactionAsync())
                {
                    try
                    {
                        using (var command = new MySqlCommand(insertQuery, _connection, transaction))
                        {
                            int processedCount = 0;
                            
                            foreach (var item in data)
                            {
                                command.Parameters.Clear();
                                var parameters = mapToParameters(item);
                                
                                foreach (var param in parameters)
                                {
                                    command.Parameters.AddWithValue($"@{param.Key}", param.Value ?? DBNull.Value);
                                }

                                await command.ExecuteNonQueryAsync();
                                processedCount++;
                            }

                            await transaction.CommitAsync();
                            RaiseStatusUpdate($"成功插入/更新 {processedCount} 条记录到表 {tableName}");
                            return true;
                        }
                    }
                    catch (Exception)
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                RaiseError($"批量插入/更新失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 断开数据库连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                if (_connection?.State == ConnectionState.Open)
                {
                    _connection.Close();
                    RaiseStatusUpdate("数据库连接已关闭");
                }
            }
            catch (Exception ex)
            {
                RaiseError($"关闭数据库连接失败: {ex.Message}");
            }
        }

        public void Dispose()
        {
            Disconnect();
            _connection?.Dispose();
        }
    }

    /// <summary>
    /// 数据库列信息
    /// </summary>
    public class ColumnInfo
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public string Null { get; set; }
        public string Key { get; set; }
        public string Default { get; set; }
        public string Extra { get; set; }

        public override string ToString()
        {
            return $"{Name} - {Type} ({(Null == "YES" ? "可空" : "非空")})";
        }
    }
}
