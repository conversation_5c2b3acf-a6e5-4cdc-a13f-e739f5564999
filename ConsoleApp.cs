using System;
using System.Linq;
using System.Threading.Tasks;
using EABDataUpdater.Models;

namespace EABDataUpdater
{
    /// <summary>
    /// 控制台版本的数据更新程序
    /// </summary>
    public class ConsoleApp
    {
        private readonly string _connectionString;
        private readonly string _symbol;
        private readonly int _daysBack;

        public ConsoleApp(string connectionString, string symbol = "0023.HK", int daysBack = 30)
        {
            _connectionString = connectionString;
            _symbol = symbol;
            _daysBack = daysBack;
        }

        /// <summary>
        /// 运行控制台应用程序
        /// </summary>
        public async Task<int> RunAsync(bool forceUpdate = false)
        {
            try
            {
                Console.WriteLine("=== EAB 0023.HK 数据更新器 (控制台版本) ===");
                Console.WriteLine($"股票代码: {_symbol}");
                Console.WriteLine($"回溯天数: {_daysBack}");
                Console.WriteLine($"更新模式: {(forceUpdate ? "强制更新" : "增量更新")}");
                Console.WriteLine($"开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();

                using (var updater = new EAB0023HKDataUpdater(_connectionString, _symbol))
                {
                    // 订阅事件
                    updater.OnStatusUpdate += (s, e) => Console.WriteLine($"[INFO] {e}");
                    updater.OnError += (s, e) => Console.WriteLine($"[ERROR] {e}");

                    // 执行更新
                    var stats = await updater.UpdateDataProcedureWithStatsAsync(_daysBack, forceUpdate);

                    // 显示结果
                    Console.WriteLine();
                    Console.WriteLine("=== 更新完成 ===");
                    Console.WriteLine($"成功: {(stats.Success ? "是" : "否")}");
                    Console.WriteLine($"总记录数: {stats.TotalRecords}");
                    Console.WriteLine($"新增记录: {stats.NewRecords}");
                    Console.WriteLine($"更新记录: {stats.UpdatedRecords}");
                    Console.WriteLine($"跳过记录: {stats.SkippedRecords}");
                    Console.WriteLine($"耗时: {stats.Duration.TotalSeconds:F1} 秒");

                    if (stats.LatestDataDate.HasValue)
                    {
                        Console.WriteLine($"最新数据日期: {stats.LatestDataDate:yyyy-MM-dd}");
                    }

                    if (!stats.Success)
                    {
                        Console.WriteLine($"错误信息: {stats.ErrorMessage}");
                    }

                    Console.WriteLine($"结束时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                    return stats.Success ? 0 : 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FATAL ERROR] {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return 1;
            }
        }

        /// <summary>
        /// 静态方法：从命令行参数运行
        /// </summary>
        public static async Task<int> RunFromArgsAsync(string[] args)
        {
            try
            {
                // 检查是否是测试模式
                if (args.Length > 1 && args[1].ToLower() == "--test-indicators")
                {
                    Console.WriteLine("运行指标计算测试...");
                    TestIndicatorCalculation.ShowIndicatorDescriptions();
                    TestIndicatorCalculation.RunTest();
                    return 0;
                }

                // 解析命令行参数
                var server = GetArgValue(args, "--server", "localhost");
                var database = GetArgValue(args, "--database", "finance");
                var username = GetArgValue(args, "--username", "root");
                var password = GetArgValue(args, "--password", "");
                var symbol = GetArgValue(args, "--symbol", "0023.HK");
                var daysBackStr = GetArgValue(args, "--days", "30");
                var forceUpdate = HasArg(args, "--force");

                if (!int.TryParse(daysBackStr, out int daysBack))
                {
                    daysBack = 30;
                }

                // 构建连接字符串
                var connectionString = $"Server={server};Database={database};Uid={username};Pwd={password};CharSet=utf8mb4;SslMode=None;";

                // 检查是否是调试模式
                if (args.Length > 1 && args[1].ToLower() == "--debug")
                {
                    await DebugUpdater.RunDebugAsync(connectionString, symbol, daysBack);
                    return 0;
                }

                // 创建并运行应用程序
                var app = new ConsoleApp(connectionString, symbol, daysBack);
                return await app.RunAsync(forceUpdate);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[FATAL ERROR] 启动失败: {ex.Message}");
                ShowUsage();
                return 1;
            }
        }

        /// <summary>
        /// 获取命令行参数值
        /// </summary>
        private static string GetArgValue(string[] args, string key, string defaultValue)
        {
            for (int i = 0; i < args.Length - 1; i++)
            {
                if (args[i].Equals(key, StringComparison.OrdinalIgnoreCase))
                {
                    return args[i + 1];
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// 检查是否有指定的参数
        /// </summary>
        private static bool HasArg(string[] args, string key)
        {
            return args.Any(arg => arg.Equals(key, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private static void ShowUsage()
        {
            Console.WriteLine();
            Console.WriteLine("使用方法:");
            Console.WriteLine("  EABDataUpdater.exe console [选项]");
            Console.WriteLine();
            Console.WriteLine("选项:");
            Console.WriteLine("  --server <服务器>     MySQL服务器地址 (默认: localhost)");
            Console.WriteLine("  --database <数据库>   数据库名称 (默认: finance)");
            Console.WriteLine("  --username <用户名>   数据库用户名 (默认: root)");
            Console.WriteLine("  --password <密码>     数据库密码 (默认: 空)");
            Console.WriteLine("  --symbol <股票代码>   股票代码 (默认: 0023.HK)");
            Console.WriteLine("  --days <天数>         回溯天数 (默认: 30)");
            Console.WriteLine("  --force               强制更新模式（重新下载最近N天数据）");
            Console.WriteLine("  --test-indicators     测试指标计算逻辑");
            Console.WriteLine("  --debug               调试模式（详细诊断信息）");
            Console.WriteLine();
            Console.WriteLine("示例:");
            Console.WriteLine("  EABDataUpdater.exe console --server localhost --username root --password mypass --days 60");
            Console.WriteLine("  EABDataUpdater.exe console --force --days 7  # 强制重新下载最近7天");
            Console.WriteLine("  EABDataUpdater.exe console --debug --days 7  # 调试模式");
            Console.WriteLine("  EABDataUpdater.exe console --test-indicators");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试所有连接
        /// </summary>
        public async Task<bool> TestConnectionsAsync()
        {
            Console.WriteLine("=== 连接测试 ===");

            try
            {
                using (var updater = new EAB0023HKDataUpdater(_connectionString, _symbol))
                {
                    // 订阅事件
                    updater.OnStatusUpdate += (s, e) => Console.WriteLine($"[INFO] {e}");
                    updater.OnError += (s, e) => Console.WriteLine($"[ERROR] {e}");

                    // 测试数据库连接
                    Console.WriteLine("测试数据库连接...");
                    var dbSuccess = await updater.TestDatabaseConnectionAsync();

                    // 测试Yahoo Finance连接
                    Console.WriteLine("测试Yahoo Finance连接...");
                    var yfSuccess = await updater.TestYahooFinanceConnectionAsync();

                    // 获取股票信息
                    if (yfSuccess)
                    {
                        Console.WriteLine("获取股票信息...");
                        var stockInfo = await updater.GetStockInfoAsync();
                        Console.WriteLine($"股票信息: {stockInfo}");
                    }

                    var allSuccess = dbSuccess && yfSuccess;
                    Console.WriteLine($"连接测试结果: {(allSuccess ? "成功" : "失败")}");

                    return allSuccess;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] 连接测试异常: {ex.Message}");
                return false;
            }
        }
    }
}
