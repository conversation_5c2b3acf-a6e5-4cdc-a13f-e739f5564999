using System;
using System.Collections.Generic;
using System.Linq;
using EABDataUpdater.Models;

namespace EABDataUpdater.Services
{
    /// <summary>
    /// 技术指标计算服务
    /// </summary>
    public class TechnicalIndicatorService
    {
        /// <summary>
        /// 计算RSI (相对强弱指数)
        /// </summary>
        public static void CalculateRSI(List<StockData> data, int period = 14)
        {
            if (data == null || data.Count < period + 1) return;

            var sortedData = data.OrderBy(x => x.Date).ToList();

            for (int i = period; i < sortedData.Count; i++)
            {
                decimal gains = 0;
                decimal losses = 0;

                for (int j = i - period + 1; j <= i; j++)
                {
                    decimal change = sortedData[j].Close - sortedData[j - 1].Close;
                    if (change > 0)
                        gains += change;
                    else
                        losses += Math.Abs(change);
                }

                decimal avgGain = gains / period;
                decimal avgLoss = losses / period;

                if (avgLoss == 0)
                {
                    sortedData[i].RSI = 100;
                }
                else
                {
                    decimal rs = avgGain / avgLoss;
                    sortedData[i].RSI = 100 - (100 / (1 + rs));
                }
            }
        }

        /// <summary>
        /// 计算MFI (资金流量指数)
        /// </summary>
        public static void CalculateMFI(List<StockData> data, int period = 14)
        {
            if (data == null || data.Count < period + 1) return;

            var sortedData = data.OrderBy(x => x.Date).ToList();

            // 首先计算每日的资金流
            for (int i = 1; i < sortedData.Count; i++)
            {
                var current = sortedData[i];
                var previous = sortedData[i - 1];

                // 计算典型价格
                current.CalculateBasicIndicators();

                // 判断资金流方向
                if (current.TypicalPrice > previous.TypicalPrice)
                {
                    current.PositiveMoneyFlow = current.MoneyFlow;
                    current.NegativeMoneyFlow = 0;
                }
                else if (current.TypicalPrice < previous.TypicalPrice)
                {
                    current.PositiveMoneyFlow = 0;
                    current.NegativeMoneyFlow = current.MoneyFlow;
                }
                else
                {
                    current.PositiveMoneyFlow = 0;
                    current.NegativeMoneyFlow = 0;
                }
            }

            // 计算MFI
            for (int i = period; i < sortedData.Count; i++)
            {
                decimal positiveFlow = 0;
                decimal negativeFlow = 0;

                for (int j = i - period + 1; j <= i; j++)
                {
                    positiveFlow += sortedData[j].PositiveMoneyFlow ?? 0;
                    negativeFlow += sortedData[j].NegativeMoneyFlow ?? 0;
                }

                if (negativeFlow == 0)
                {
                    sortedData[i].MFI = 100;
                }
                else
                {
                    sortedData[i].MoneyFlowRatio = positiveFlow / negativeFlow;
                    sortedData[i].MFI = 100 - (100 / (1 + sortedData[i].MoneyFlowRatio.Value));
                }
            }
        }

        /// <summary>
        /// 计算简单移动平均线 (SMA)
        /// </summary>
        public static List<decimal?> CalculateSMA(List<StockData> data, int period)
        {
            var result = new List<decimal?>();
            var sortedData = data.OrderBy(x => x.Date).ToList();

            for (int i = 0; i < sortedData.Count; i++)
            {
                if (i < period - 1)
                {
                    result.Add(null);
                }
                else
                {
                    decimal sum = 0;
                    for (int j = i - period + 1; j <= i; j++)
                    {
                        sum += sortedData[j].Close;
                    }
                    result.Add(sum / period);
                }
            }

            return result;
        }

        /// <summary>
        /// 计算指数移动平均线 (EMA)
        /// </summary>
        public static List<decimal?> CalculateEMA(List<StockData> data, int period)
        {
            var result = new List<decimal?>();
            var sortedData = data.OrderBy(x => x.Date).ToList();

            if (sortedData.Count == 0) return result;

            decimal multiplier = 2m / (period + 1);

            for (int i = 0; i < sortedData.Count; i++)
            {
                if (i == 0)
                {
                    result.Add(sortedData[i].Close);
                }
                else
                {
                    decimal ema = (sortedData[i].Close * multiplier) + (result[i - 1].Value * (1 - multiplier));
                    result.Add(ema);
                }
            }

            return result;
        }

        /// <summary>
        /// 计算布林带
        /// </summary>
        public static (List<decimal?> upper, List<decimal?> middle, List<decimal?> lower)
            CalculateBollingerBands(List<StockData> data, int period = 20, decimal stdDevMultiplier = 2)
        {
            var upper = new List<decimal?>();
            var middle = new List<decimal?>();
            var lower = new List<decimal?>();

            var sortedData = data.OrderBy(x => x.Date).ToList();
            var sma = CalculateSMA(data, period);

            for (int i = 0; i < sortedData.Count; i++)
            {
                if (i < period - 1)
                {
                    upper.Add(null);
                    middle.Add(null);
                    lower.Add(null);
                }
                else
                {
                    // 计算标准差
                    decimal sum = 0;
                    for (int j = i - period + 1; j <= i; j++)
                    {
                        decimal diff = sortedData[j].Close - sma[i].Value;
                        sum += diff * diff;
                    }
                    decimal stdDev = (decimal)Math.Sqrt((double)(sum / period));

                    middle.Add(sma[i]);
                    upper.Add(sma[i] + (stdDev * stdDevMultiplier));
                    lower.Add(sma[i] - (stdDev * stdDevMultiplier));
                }
            }

            return (upper, middle, lower);
        }

        /// <summary>
        /// 计算随机指标 (Stochastic)
        /// </summary>
        public static (List<decimal?> k, List<decimal?> d) CalculateStochastic(List<StockData> data, int kPeriod = 14, int dPeriod = 3)
        {
            var kValues = new List<decimal?>();
            var dValues = new List<decimal?>();
            var sortedData = data.OrderBy(x => x.Date).ToList();

            // 计算%K
            for (int i = 0; i < sortedData.Count; i++)
            {
                if (i < kPeriod - 1)
                {
                    kValues.Add(null);
                }
                else
                {
                    decimal highest = sortedData.Skip(i - kPeriod + 1).Take(kPeriod).Max(x => x.High);
                    decimal lowest = sortedData.Skip(i - kPeriod + 1).Take(kPeriod).Min(x => x.Low);

                    if (highest == lowest)
                    {
                        kValues.Add(50); // 避免除零
                    }
                    else
                    {
                        decimal k = ((sortedData[i].Close - lowest) / (highest - lowest)) * 100;
                        kValues.Add(k);
                    }
                }
            }

            // 计算%D (K的移动平均)
            for (int i = 0; i < kValues.Count; i++)
            {
                if (i < dPeriod - 1 || kValues[i] == null)
                {
                    dValues.Add(null);
                }
                else
                {
                    decimal sum = 0;
                    int count = 0;
                    for (int j = i - dPeriod + 1; j <= i; j++)
                    {
                        if (kValues[j].HasValue)
                        {
                            sum += kValues[j].Value;
                            count++;
                        }
                    }
                    dValues.Add(count > 0 ? sum / count : (decimal?)null);
                }
            }

            return (kValues, dValues);
        }

        /// <summary>
        /// 计算MACD
        /// </summary>
        public static (List<decimal?> macd, List<decimal?> signal, List<decimal?> histogram)
            CalculateMACD(List<StockData> data, int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9)
        {
            var ema12 = CalculateEMA(data, fastPeriod);
            var ema26 = CalculateEMA(data, slowPeriod);

            var macd = new List<decimal?>();
            var signal = new List<decimal?>();
            var histogram = new List<decimal?>();

            // 计算MACD线
            for (int i = 0; i < data.Count; i++)
            {
                if (ema12[i].HasValue && ema26[i].HasValue)
                {
                    macd.Add(ema12[i].Value - ema26[i].Value);
                }
                else
                {
                    macd.Add(null);
                }
            }

            // 计算信号线 (MACD的EMA)
            var macdData = macd.Select((value, index) => new StockData
            {
                Date = data[index].Date,
                Close = value ?? 0
            }).ToList();

            var signalEMA = CalculateEMA(macdData, signalPeriod);

            for (int i = 0; i < macd.Count; i++)
            {
                if (macd[i].HasValue && i >= slowPeriod - 1)
                {
                    signal.Add(signalEMA[i]);
                }
                else
                {
                    signal.Add(null);
                }
            }

            // 计算柱状图
            for (int i = 0; i < macd.Count; i++)
            {
                if (macd[i].HasValue && signal[i].HasValue)
                {
                    histogram.Add(macd[i].Value - signal[i].Value);
                }
                else
                {
                    histogram.Add(null);
                }
            }

            return (macd, signal, histogram);
        }

        /// <summary>
        /// 批量计算所有技术指标
        /// </summary>
        public static void CalculateAllIndicators(List<StockData> data)
        {
            if (data == null || data.Count == 0) return;

            // 计算基本指标
            foreach (var item in data)
            {
                item.CalculateBasicIndicators();
            }

            // 计算标准技术指标
            CalculateRSI(data, 14);
            CalculateMFI(data, 14);

            // 计算自定义指标
            CustomIndicatorService.CalculateCustomIndicators(data);
        }
    }
}
