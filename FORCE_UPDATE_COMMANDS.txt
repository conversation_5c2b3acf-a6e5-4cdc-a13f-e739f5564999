EAB 0023.HK Force Update Commands
=====================================

If you're having encoding issues with batch files, use these direct commands:

1. BUILD THE PROJECT FIRST:
   dotnet build --configuration Release

2. FORCE UPDATE COMMANDS (replace 'yourpassword' with your actual password):

   # Force update last 7 days (recommended for missing July 29th data)
   dotnet run --configuration Release console --server localhost --database finance --username root --password yourpassword --symbol 0023.HK --days 7 --force

   # Force update last 3 days (minimal update)
   dotnet run --configuration Release console --server localhost --database finance --username root --password yourpassword --symbol 0023.HK --days 3 --force

   # Force update last 30 days (full recent update)
   dotnet run --configuration Release console --server localhost --database finance --username root --password yourpassword --symbol 0023.HK --days 30 --force

3. ALTERNATIVE - USE POWERSHELL:
   PowerShell -ExecutionPolicy Bypass -File ForceUpdate.ps1

4. ALTERNATIVE - USE SIMPLE BATCH:
   quick_force_update.bat

5. CHECK RESULTS:
   dotnet run --configuration Release indicators

WHAT FORCE UPDATE DOES:
- Re-downloads the last N days of data from Yahoo Finance
- Overwrites existing data for those dates
- Recalculates all indicators (Controller, Full_Y, E, i, etc.)
- Ensures missing dates like July 29th are included

EXAMPLE FOR YOUR CASE:
If you deleted July 29th data and want to restore it:
dotnet run --configuration Release console --server localhost --database finance --username root --password yourpassword --days 7 --force

This will re-download July 23-29 data and restore the missing July 29th record.
