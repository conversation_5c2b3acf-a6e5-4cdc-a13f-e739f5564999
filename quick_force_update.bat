@echo off
echo ========================================
echo Quick Force Update - EAB 0023.HK
echo ========================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET SDK not installed
    pause
    exit /b 1
)

echo Building project...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo This will force update the last 7 days of data for 0023.HK
echo Please enter your database password (other settings use defaults):
echo - Server: localhost
echo - Database: finance  
echo - Username: root
echo - Symbol: 0023.HK
echo - Days: 7
echo.

set /p password="MySQL Password: "

echo.
echo Starting force update...
echo.

dotnet run --configuration Release console --server localhost --database finance --username root --password %password% --symbol 0023.HK --days 7 --force

echo.
if %errorlevel% equ 0 (
    echo SUCCESS: Force update completed!
    echo You should now have July 29th data.
) else (
    echo FAILED: Force update failed!
)
echo.
pause
