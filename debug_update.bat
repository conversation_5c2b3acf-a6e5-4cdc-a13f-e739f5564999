@echo off
echo ========================================
echo EAB 0023.HK Debug Mode
echo ========================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET SDK not installed
    pause
    exit /b 1
)

echo Building project...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Debug mode will test all components step by step:
echo 1. Database connection
echo 2. Yahoo Finance connection  
echo 3. Data download
echo 4. Indicator calculation
echo 5. Database insert
echo.

set /p password="Enter MySQL password: "

echo.
echo Starting debug mode...
echo.

dotnet run --configuration Release console --debug --server localhost --database finance --username root --password %password% --symbol 0023.HK --days 7

echo.
pause
