@echo off
echo ========================================
echo EAB 0023.HK Force Update
echo ========================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET SDK not installed or not in PATH
    pause
    exit /b 1
)

echo Building project...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo Error: Project build failed
    pause
    exit /b 1
)

echo.
echo Force Update Mode:
echo - Will re-download recent N days of data
echo - May overwrite existing data
echo - Use to fix missing or incorrect data
echo.

echo Please enter database connection info:
set /p server="MySQL Server (default: localhost): "
if "%server%"=="" set server=localhost

set /p database="Database Name (default: finance): "
if "%database%"=="" set database=finance

set /p username="Username (default: root): "
if "%username%"=="" set username=root

set /p password="Password: "

set /p symbol="Stock Symbol (default: 0023.HK): "
if "%symbol%"=="" set symbol=0023.HK

set /p days="Days to force update (default: 7): "
if "%days%"=="" set days=7

echo.
echo Confirm force update settings:
echo Server: %server%
echo Database: %database%
echo Username: %username%
echo Symbol: %symbol%
echo Days: %days%
echo.
echo WARNING: This will re-download the last %days% days of data and may overwrite existing data!
echo.
set /p confirm="Are you sure you want to continue? (y/N): "

if /i not "%confirm%"=="y" (
    echo Operation cancelled
    pause
    exit /b 0
)

echo.
echo Starting force update...
echo.

dotnet run --configuration Release console --server %server% --database %database% --username %username% --password %password% --symbol %symbol% --days %days% --force

echo.
if %errorlevel% equ 0 (
    echo Force update completed successfully!
) else (
    echo Force update failed!
)
echo.
echo Press any key to exit...
pause >nul
