@echo off
echo ========================================
echo EAB 0023.HK 数据强制更新
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    pause
    exit /b 1
)

echo 正在编译项目...
dotnet build --configuration Release >nul 2>&1

if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 强制更新模式说明:
echo - 将重新下载最近N天的数据
echo - 可能会覆盖现有数据
echo - 适用于修复缺失或错误的数据
echo.

echo 请输入数据库连接信息:
set /p server="MySQL服务器 (默认: localhost): "
if "%server%"=="" set server=localhost

set /p database="数据库名称 (默认: finance): "
if "%database%"=="" set database=finance

set /p username="用户名 (默认: root): "
if "%username%"=="" set username=root

set /p password="密码: "

set /p symbol="股票代码 (默认: 0023.HK): "
if "%symbol%"=="" set symbol=0023.HK

set /p days="强制更新天数 (默认: 7): "
if "%days%"=="" set days=7

echo.
echo 确认强制更新设置:
echo 服务器: %server%
echo 数据库: %database%
echo 用户名: %username%
echo 股票代码: %symbol%
echo 更新天数: %days%
echo.
echo 警告: 这将重新下载最近 %days% 天的数据，可能覆盖现有数据！
echo.
set /p confirm="确定要继续吗？(y/N): "

if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 开始强制更新数据...
echo.

dotnet run --configuration Release console --server %server% --database %database% --username %username% --password %password% --symbol %symbol% --days %days% --force

echo.
if %errorlevel% equ 0 (
    echo 强制更新完成！
) else (
    echo 强制更新失败！
)
echo.
echo 按任意键退出...
pause >nul
