using System;
using System.Configuration;

namespace EABDataUpdater
{
    /// <summary>
    /// 应用程序配置类
    /// </summary>
    public static class AppConfig
    {
        /// <summary>
        /// 数据库配置
        /// </summary>
        public static class Database
        {
            public static string Server => GetSetting("Database.Server", "localhost");
            public static string DatabaseName => GetSetting("Database.Name", "finance");
            public static string Username => GetSetting("Database.Username", "root");
            public static string Password => GetSetting("Database.Password", "");
            public static int Port => int.Parse(GetSetting("Database.Port", "3306"));
            
            public static string GetConnectionString(string server = null, string database = null, 
                string username = null, string password = null, int? port = null)
            {
                return $"Server={server ?? Server};" +
                       $"Database={database ?? DatabaseName};" +
                       $"Uid={username ?? Username};" +
                       $"Pwd={password ?? Password};" +
                       $"Port={port ?? Port};" +
                       $"CharSet=utf8mb4;" +
                       $"SslMode=None;" +
                       $"AllowUserVariables=True;";
            }
        }

        /// <summary>
        /// Yahoo Finance配置
        /// </summary>
        public static class YahooFinance
        {
            public static string Symbol => GetSetting("YahooFinance.Symbol", "0023.HK");
            public static int DefaultDaysBack => int.Parse(GetSetting("YahooFinance.DefaultDaysBack", "30"));
            public static int TimeoutSeconds => int.Parse(GetSetting("YahooFinance.TimeoutSeconds", "30"));
        }

        /// <summary>
        /// 获取配置设置
        /// </summary>
        private static string GetSetting(string key, string defaultValue = "")
        {
            try
            {
                return ConfigurationManager.AppSettings[key] ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
