using System;
using System.Threading.Tasks;
using EABDataUpdater.Services;

namespace EABDataUpdater
{
    /// <summary>
    /// 调试版本的数据更新器
    /// </summary>
    public class DebugUpdater
    {
        public static async Task RunDebugAsync(string connectionString, string symbol = "0023.HK", int days = 7)
        {
            Console.WriteLine("=== DEBUG MODE: EAB Data Updater ===");
            Console.WriteLine($"Symbol: {symbol}");
            Console.WriteLine($"Days: {days}");
            Console.WriteLine($"Connection: {connectionString.Replace("Pwd=", "Pwd=***")}");
            Console.WriteLine();

            try
            {
                // 1. 测试数据库连接
                Console.WriteLine("1. Testing database connection...");
                using (var dbService = new DatabaseService(connectionString))
                {
                    dbService.OnStatusUpdate += (s, e) => Console.WriteLine($"   [DB] {e}");
                    dbService.OnError += (s, e) => Console.WriteLine($"   [DB ERROR] {e}");

                    var dbConnected = await dbService.TestConnectionAsync();
                    Console.WriteLine($"   Database connected: {dbConnected}");

                    if (!dbConnected)
                    {
                        Console.WriteLine("   FAILED: Cannot connect to database!");
                        return;
                    }

                    // 检查表
                    var tableExists = await dbService.CheckTableExistsAsync("eab_0023hk");
                    Console.WriteLine($"   Table exists: {tableExists}");

                    if (tableExists)
                    {
                        var recordCount = await dbService.GetTableRecordCountAsync("eab_0023hk");
                        var latestDate = await dbService.GetLatestDateAsync("eab_0023hk");
                        Console.WriteLine($"   Record count: {recordCount}");
                        Console.WriteLine($"   Latest date: {latestDate?.ToString("yyyy-MM-dd") ?? "None"}");
                    }
                }

                Console.WriteLine();

                // 2. 测试Yahoo Finance连接
                Console.WriteLine("2. Testing Yahoo Finance connection...");
                using (var yfService = new YahooFinanceService(symbol))
                {
                    yfService.OnStatusUpdate += (s, e) => Console.WriteLine($"   [YF] {e}");
                    yfService.OnError += (s, e) => Console.WriteLine($"   [YF ERROR] {e}");

                    var yfConnected = await yfService.TestConnectionAsync();
                    Console.WriteLine($"   Yahoo Finance connected: {yfConnected}");

                    if (yfConnected)
                    {
                        var stockInfo = await yfService.GetStockInfoAsync();
                        Console.WriteLine($"   Stock info: {stockInfo}");
                    }
                }

                Console.WriteLine();

                // 3. 测试数据下载
                Console.WriteLine("3. Testing data download...");
                var startDate = DateTime.Now.AddDays(-days);
                var endDate = DateTime.Now;

                Console.WriteLine($"   Download range: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                var data = new List<EABDataUpdater.Models.StockData>();

                using (var yfService = new YahooFinanceService(symbol))
                {
                    yfService.OnStatusUpdate += (s, e) => Console.WriteLine($"   [YF] {e}");
                    yfService.OnError += (s, e) => Console.WriteLine($"   [YF ERROR] {e}");

                    data = await yfService.DownloadHistoricalDataAsync(startDate, endDate);
                    Console.WriteLine($"   Downloaded records: {data.Count}");

                    if (data.Count > 0)
                    {
                        Console.WriteLine("   Sample data:");
                        for (int i = Math.Max(0, data.Count - 3); i < data.Count; i++)
                        {
                            var item = data[i];
                            Console.WriteLine($"     {item.Date:yyyy-MM-dd}: O={item.Open:F2} H={item.High:F2} L={item.Low:F2} C={item.Close:F2} V={item.Volume:N0}");
                        }

                        // 计算指标
                        Console.WriteLine("   Calculating indicators...");
                        TechnicalIndicatorService.CalculateAllIndicators(data);

                        Console.WriteLine("   Sample indicators:");
                        var lastItem = data[data.Count - 1];
                        Console.WriteLine($"     TypicalPrice: {lastItem.TypicalPrice:F4}");
                        Console.WriteLine($"     MoneyFlow: {lastItem.MoneyFlow:N0}");
                        Console.WriteLine($"     MidPrice: {lastItem.MidPrice:F4}");
                        Console.WriteLine($"     Controller: {lastItem.Controller}");
                        Console.WriteLine($"     Full_Y: {lastItem.Full_Y:F6}");
                        Console.WriteLine($"     E: {lastItem.E:F6}");
                        Console.WriteLine($"     i: {lastItem.I}");
                    }
                    else
                    {
                        Console.WriteLine("   WARNING: No data downloaded!");
                        Console.WriteLine("   Possible reasons:");
                        Console.WriteLine("   - Date range is in the future");
                        Console.WriteLine("   - No trading days in the range");
                        Console.WriteLine("   - Yahoo Finance API issue");
                        Console.WriteLine("   - Network connectivity issue");
                    }
                }

                Console.WriteLine();

                // 4. 测试数据库插入
                if (data.Count > 0)
                {
                    Console.WriteLine("4. Testing database insert...");
                    using (var dbService = new DatabaseService(connectionString))
                    {
                        dbService.OnStatusUpdate += (s, e) => Console.WriteLine($"   [DB] {e}");
                        dbService.OnError += (s, e) => Console.WriteLine($"   [DB ERROR] {e}");

                        var insertQuery = @"
                            INSERT INTO eab_0023hk (
                                Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
                                PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
                                Y_Value, X_Value, E_Value, RSI, TradingSignal, i, midprice,
                                Controller, Full_Y, E
                            ) VALUES (
                                @Date, @Open, @High, @Low, @Close, @Volume, @TypicalPrice, @MoneyFlow,
                                @PositiveMoneyFlow, @NegativeMoneyFlow, @MoneyFlowRatio, @MFI,
                                @Y_Value, @X_Value, @E_Value, @RSI, @TradingSignal, @i, @midprice,
                                @Controller, @Full_Y, @E
                            ) ON DUPLICATE KEY UPDATE
                                Open = VALUES(Open),
                                High = VALUES(High),
                                Low = VALUES(Low),
                                Close = VALUES(Close),
                                Volume = VALUES(Volume),
                                TypicalPrice = VALUES(TypicalPrice),
                                MoneyFlow = VALUES(MoneyFlow),
                                PositiveMoneyFlow = VALUES(PositiveMoneyFlow),
                                NegativeMoneyFlow = VALUES(NegativeMoneyFlow),
                                MoneyFlowRatio = VALUES(MoneyFlowRatio),
                                MFI = VALUES(MFI),
                                Y_Value = VALUES(Y_Value),
                                X_Value = VALUES(X_Value),
                                E_Value = VALUES(E_Value),
                                RSI = VALUES(RSI),
                                TradingSignal = VALUES(TradingSignal),
                                i = VALUES(i),
                                midprice = VALUES(midprice),
                                Controller = VALUES(Controller),
                                Full_Y = VALUES(Full_Y),
                                E = VALUES(E),
                                updated_at = CURRENT_TIMESTAMP";

                        var success = await dbService.BulkInsertOrUpdateAsync(
                            "eab_0023hk",
                            data,
                            item => item.ToParameterDictionary(),
                            insertQuery);

                        Console.WriteLine($"   Insert success: {success}");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("=== DEBUG COMPLETED ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG ERROR: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
