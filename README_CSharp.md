# EAB 0023.HK 数据更新器 (C# Windows Forms版本)

这是一个C# Windows Forms应用程序，用于从Yahoo Finance下载东亚银行(0023.HK)的股票数据并更新到MySQL数据库中。

## 功能特性

- ✅ Windows Forms图形界面
- ✅ 点击btnCalc按钮更新数据
- ✅ 从Yahoo Finance自动下载0023.HK股票数据
- ✅ 智能增量更新（只下载数据库中没有的新数据）
- ✅ 计算基本技术指标（TypicalPrice, MoneyFlow等）
- ✅ 支持数据插入和更新（避免重复数据）
- ✅ 实时状态显示和日志记录
- ✅ 异步操作，界面不会卡死
- ✅ 完整的错误处理机制

## 系统要求

- .NET 6.0 或更高版本
- Windows 操作系统
- MySQL/MariaDB 数据库
- 网络连接（访问Yahoo Finance）

## 安装和配置

### 1. 安装.NET 6.0 SDK
从 [Microsoft官网](https://dotnet.microsoft.com/download/dotnet/6.0) 下载并安装.NET 6.0 SDK

### 2. 编译项目
```bash
# 在项目目录中运行
dotnet build
```

### 3. 配置数据库连接
在应用程序界面中配置数据库连接信息：
- 服务器: localhost (或您的MySQL服务器地址)
- 数据库: finance
- 用户名: root (或您的MySQL用户名)
- 密码: (您的MySQL密码)
- 回溯天数: 30 (首次运行时下载的历史数据天数)

### 4. 运行应用程序
```bash
dotnet run
```

## 使用方法

1. **启动应用程序**
   - 运行编译后的exe文件或使用 `dotnet run`

2. **配置数据库连接**
   - 在界面上方的"数据库配置"区域填入正确的数据库连接信息

3. **更新数据**
   - 点击"更新数据"按钮(btnCalc)
   - 程序会自动：
     - 测试数据库连接
     - 获取数据库中最新的数据日期
     - 从Yahoo Finance下载新数据
     - 更新到数据库中

4. **查看日志**
   - 在下方的黑色日志区域可以看到详细的操作过程
   - 绿色文字显示正常信息
   - 红色文字显示错误信息

## 界面说明

### 数据库配置区域
- **服务器**: MySQL服务器地址
- **数据库**: 数据库名称 (finance)
- **用户名**: 数据库用户名
- **密码**: 数据库密码
- **回溯天数**: 首次运行或没有数据时，回溯下载的天数

### 控制区域
- **更新数据按钮**: 点击开始数据更新过程
- **状态显示**: 显示当前操作状态
- **进度条**: 数据更新时显示进度动画

### 日志区域
- 显示详细的操作日志
- 包含时间戳的信息、错误和异常记录
- 支持滚动查看历史日志

## 数据库表结构

程序会更新 `finance.eab_0023hk` 表，包含以下主要字段：

```sql
- Date: 交易日期 (主键)
- Open, High, Low, Close: OHLC价格数据
- Volume: 交易量
- TypicalPrice: 典型价格 (High+Low+Close)/3
- MoneyFlow: 资金流 (TypicalPrice * Volume)
- 其他技术指标字段（预留用于后续计算）
```

## 技术实现

### 核心类说明

1. **EAB0023HKDataUpdater**: 主要的数据更新逻辑类
   - 负责数据库连接
   - Yahoo Finance数据下载
   - 技术指标计算
   - 数据插入/更新

2. **MainForm**: Windows Forms主窗体
   - 用户界面
   - 事件处理
   - 异步操作管理

3. **StockData**: 股票数据模型
   - 封装股票的OHLCV数据
   - 技术指标数据

4. **AppConfig**: 配置管理类
   - 管理应用程序配置
   - 数据库连接字符串生成

### 异步操作
- 所有数据库和网络操作都是异步的
- 使用async/await模式避免界面卡死
- 支持操作取消和超时处理

### 错误处理
- 完整的异常捕获和处理
- 数据库事务回滚机制
- 用户友好的错误信息显示

## 定时任务

可以通过Windows任务计划程序设置定时运行：

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每日运行）
4. 设置操作为运行此程序的exe文件

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 验证连接信息是否正确
   - 确认防火墙设置

2. **Yahoo Finance数据下载失败**
   - 检查网络连接
   - 确认Yahoo Finance服务可用
   - 检查股票代码是否正确

3. **数据插入失败**
   - 检查数据库表是否存在
   - 验证表结构是否匹配
   - 检查数据库权限

### 日志分析
- `[INFO]`: 正常操作信息
- `[ERROR]`: 错误信息
- `[EXCEPTION]`: 异常信息

## 扩展功能

可以轻松扩展的功能：
- 支持多个股票代码
- 更多技术指标计算
- 数据导出功能
- 图表显示
- 邮件通知

## 注意事项

1. 确保数据库服务正在运行
2. 首次运行时会下载较多历史数据
3. 程序会自动处理重复数据
4. 建议在交易时间后运行以获取最新数据
5. 保持网络连接稳定
