#!/usr/bin/env python3
"""
测试数据库连接和Yahoo Finance数据下载
"""

import yfinance as yf
import mysql.connector
from mysql.connector import Error
from config import DATABASE_CONFIG, YAHOO_FINANCE_CONFIG
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接"""
    try:
        logger.info("测试数据库连接...")
        connection = mysql.connector.connect(**DATABASE_CONFIG)
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logger.info(f"成功连接到MySQL数据库，版本: {version[0]}")
            
            # 测试表是否存在
            cursor.execute("SHOW TABLES LIKE 'eab_0023hk'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                logger.info("表 eab_0023hk 存在")
                
                # 获取表结构
                cursor.execute("DESCRIBE eab_0023hk")
                columns = cursor.fetchall()
                logger.info("表结构:")
                for column in columns:
                    logger.info(f"  {column[0]} - {column[1]}")
                
                # 获取记录数
                cursor.execute("SELECT COUNT(*) FROM eab_0023hk")
                count = cursor.fetchone()[0]
                logger.info(f"当前记录数: {count}")
                
                # 获取最新日期
                cursor.execute("SELECT MAX(Date) FROM eab_0023hk")
                latest_date = cursor.fetchone()[0]
                logger.info(f"最新数据日期: {latest_date}")
                
            else:
                logger.warning("表 eab_0023hk 不存在")
            
            cursor.close()
            connection.close()
            return True
            
    except Error as e:
        logger.error(f"数据库连接失败: {e}")
        return False

def test_yahoo_finance():
    """测试Yahoo Finance数据下载"""
    try:
        logger.info("测试Yahoo Finance数据下载...")
        symbol = YAHOO_FINANCE_CONFIG['symbol']
        
        ticker = yf.Ticker(symbol)
        
        # 获取股票信息
        info = ticker.info
        logger.info(f"股票名称: {info.get('longName', 'N/A')}")
        logger.info(f"股票代码: {symbol}")
        logger.info(f"货币: {info.get('currency', 'N/A')}")
        logger.info(f"交易所: {info.get('exchange', 'N/A')}")
        
        # 下载最近5天的数据
        data = ticker.history(period="5d")
        
        if not data.empty:
            logger.info(f"成功下载 {len(data)} 条记录")
            logger.info("最近的数据:")
            logger.info(data.tail().to_string())
            return True
        else:
            logger.warning("没有下载到数据")
            return False
            
    except Exception as e:
        logger.error(f"Yahoo Finance数据下载失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试...")
    
    # 测试数据库连接
    db_success = test_database_connection()
    
    print("-" * 50)
    
    # 测试Yahoo Finance
    yf_success = test_yahoo_finance()
    
    print("-" * 50)
    
    if db_success and yf_success:
        logger.info("所有测试通过！可以运行主程序。")
        return 0
    else:
        logger.error("测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    exit(main())
