#!/usr/bin/env python3
"""
存储过程：从Yahoo Finance下载0023.HK数据并更新到数据库
"""

import yfinance as yf
import mysql.connector
from mysql.connector import Error
import pandas as pd
from datetime import datetime, timedelta
import logging
import sys
from typing import Optional
from config import DATABASE_CONFIG, YAHOO_FINANCE_CONFIG, LOGGING_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['log_file']),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EAB0023HKDataUpdater:
    """0023.HK数据更新器"""

    def __init__(self, db_config: dict):
        """
        初始化数据更新器

        Args:
            db_config: 数据库连接配置
        """
        self.db_config = db_config
        self.connection = None
        self.symbol = YAHOO_FINANCE_CONFIG['symbol']  # 东亚银行股票代码

    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logger.info("成功连接到MySQL数据库")
                return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def disconnect_database(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")

    def get_latest_date_from_db(self) -> Optional[datetime]:
        """获取数据库中最新的日期"""
        try:
            cursor = self.connection.cursor()
            query = "SELECT MAX(Date) FROM eab_0023hk"
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            if result[0]:
                logger.info(f"数据库中最新日期: {result[0]}")
                return result[0]
            else:
                logger.info("数据库中没有数据")
                return None

        except Error as e:
            logger.error(f"获取最新日期失败: {e}")
            return None

    def download_yahoo_finance_data(self, start_date: str = None, end_date: str = None) -> Optional[pd.DataFrame]:
        """
        从Yahoo Finance下载数据

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            DataFrame或None
        """
        try:
            # 如果没有指定开始日期，默认下载最近30天的数据
            if not start_date:
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            logger.info(f"正在下载 {self.symbol} 从 {start_date} 到 {end_date} 的数据...")

            # 下载数据
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(start=start_date, end=end_date)

            if data.empty:
                logger.warning("没有下载到数据")
                return None

            # 重置索引，将日期作为列
            data.reset_index(inplace=True)

            # 重命名列以匹配数据库字段
            data.rename(columns={
                'Date': 'Date',
                'Open': 'Open',
                'High': 'High',
                'Low': 'Low',
                'Close': 'Close',
                'Volume': 'Volume'
            }, inplace=True)

            # 计算技术指标
            data = self.calculate_technical_indicators(data)

            logger.info(f"成功下载 {len(data)} 条记录")
            return data

        except Exception as e:
            logger.error(f"下载Yahoo Finance数据失败: {e}")
            return None

    def calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            # 计算TypicalPrice
            data['TypicalPrice'] = (data['High'] + data['Low'] + data['Close']) / 3

            # 计算MoneyFlow
            data['MoneyFlow'] = data['TypicalPrice'] * data['Volume']

            # 初始化其他字段为NULL
            data['PositiveMoneyFlow'] = None
            data['NegativeMoneyFlow'] = None
            data['MoneyFlowRatio'] = None
            data['MFI'] = None
            data['Y_Value'] = None
            data['X_Value'] = None
            data['E_Value'] = None
            data['RSI'] = None
            data['TradingSignal'] = 0
            data['i'] = None
            data['midprice'] = None
            data['Controller'] = None
            data['Full_Y'] = None
            data['E'] = None

            return data

        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return data

    def insert_or_update_data(self, data: pd.DataFrame) -> bool:
        """插入或更新数据到数据库"""
        try:
            cursor = self.connection.cursor()

            # 准备SQL语句 - 使用ON DUPLICATE KEY UPDATE
            insert_query = """
            INSERT INTO eab_0023hk (
                Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
                PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
                Y_Value, X_Value, E_Value, RSI, TradingSignal, i, midprice,
                Controller, Full_Y, E
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) ON DUPLICATE KEY UPDATE
                Open = VALUES(Open),
                High = VALUES(High),
                Low = VALUES(Low),
                Close = VALUES(Close),
                Volume = VALUES(Volume),
                TypicalPrice = VALUES(TypicalPrice),
                MoneyFlow = VALUES(MoneyFlow),
                updated_at = CURRENT_TIMESTAMP
            """

            # 准备数据
            records = []
            for _, row in data.iterrows():
                record = (
                    row['Date'].date() if hasattr(row['Date'], 'date') else row['Date'],
                    float(row['Open']),
                    float(row['High']),
                    float(row['Low']),
                    float(row['Close']),
                    int(row['Volume']),
                    float(row['TypicalPrice']) if pd.notna(row['TypicalPrice']) else None,
                    float(row['MoneyFlow']) if pd.notna(row['MoneyFlow']) else None,
                    row['PositiveMoneyFlow'],
                    row['NegativeMoneyFlow'],
                    row['MoneyFlowRatio'],
                    row['MFI'],
                    row['Y_Value'],
                    row['X_Value'],
                    row['E_Value'],
                    row['RSI'],
                    row['TradingSignal'],
                    row['i'],
                    row['midprice'],
                    row['Controller'],
                    row['Full_Y'],
                    row['E']
                )
                records.append(record)

            # 批量插入/更新
            cursor.executemany(insert_query, records)
            self.connection.commit()

            logger.info(f"成功插入/更新 {cursor.rowcount} 条记录")
            cursor.close()
            return True

        except Error as e:
            logger.error(f"插入/更新数据失败: {e}")
            self.connection.rollback()
            return False

    def update_data_procedure(self, days_back: int = 30) -> bool:
        """
        主要的数据更新存储过程

        Args:
            days_back: 回溯天数，默认30天

        Returns:
            bool: 更新是否成功
        """
        try:
            # 连接数据库
            if not self.connect_database():
                return False

            # 获取数据库中最新日期
            latest_date = self.get_latest_date_from_db()

            # 确定下载的开始日期
            if latest_date:
                start_date = (latest_date + timedelta(days=1)).strftime('%Y-%m-%d')
            else:
                start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')

            # 下载数据
            data = self.download_yahoo_finance_data(start_date=start_date)

            if data is None or data.empty:
                logger.info("没有新数据需要更新")
                return True

            # 插入/更新数据
            success = self.insert_or_update_data(data)

            return success

        except Exception as e:
            logger.error(f"数据更新过程失败: {e}")
            return False
        finally:
            self.disconnect_database()


def main():
    """主函数"""
    # 创建更新器实例
    updater = EAB0023HKDataUpdater(DATABASE_CONFIG)

    # 执行数据更新
    success = updater.update_data_procedure(days_back=YAHOO_FINANCE_CONFIG['default_days_back'])

    if success:
        logger.info("数据更新完成")
        return 0
    else:
        logger.error("数据更新失败")
        return 1


if __name__ == "__main__":
    exit(main())
