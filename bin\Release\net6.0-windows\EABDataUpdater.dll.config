<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <appSettings>
        <!-- 数据库配置 -->
        <add key="Database.Server" value="localhost" />
        <add key="Database.Name" value="finance" />
        <add key="Database.Username" value="root" />
        <add key="Database.Password" value="" />
        <add key="Database.Port" value="3306" />
        
        <!-- Yahoo Finance配置 -->
        <add key="YahooFinance.Symbol" value="0023.HK" />
        <add key="YahooFinance.DefaultDaysBack" value="30" />
        <add key="YahooFinance.TimeoutSeconds" value="30" />
    </appSettings>
    
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
</configuration>
