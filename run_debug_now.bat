@echo off
echo ========================================
echo Quick Debug - EAB 0023.HK
echo ========================================
echo.

echo Building project (clean)...
dotnet clean >nul 2>&1
dotnet build --configuration Release

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Enter your MySQL password to run debug mode:
set /p password="Password: "

echo.
echo Running debug mode...
echo This will show detailed information about each step.
echo.

dotnet run --configuration Release console --debug --server localhost --database finance --username root --password %password% --symbol 0023.HK --days 7

echo.
echo Debug completed. Check the output above for any issues.
pause
