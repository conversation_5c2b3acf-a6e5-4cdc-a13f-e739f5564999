# EAB 0023.HK 数据更新存储过程

这个项目提供了一个自动化的存储过程，用于从Yahoo Finance下载东亚银行(0023.HK)的股票数据并更新到MySQL数据库中。

## 功能特性

- 自动从Yahoo Finance下载0023.HK股票数据
- 智能增量更新（只下载数据库中没有的新数据）
- 计算基本技术指标（TypicalPrice, MoneyFlow等）
- 支持数据插入和更新（避免重复数据）
- 完整的日志记录
- 错误处理和回滚机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 修改 `config.py` 文件中的数据库连接信息：

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'database': 'finance',
    'user': 'your_username',      # 修改为您的数据库用户名
    'password': 'your_password',  # 修改为您的数据库密码
    'port': 3306,
    'charset': 'utf8mb4',
    'autocommit': False,
    'raise_on_warnings': True
}
```

## 使用方法

### 基本使用

```bash
python update_eab_0023hk_data.py
```

### 作为模块使用

```python
from update_eab_0023hk_data import EAB0023HKDataUpdater
from config import DATABASE_CONFIG

# 创建更新器实例
updater = EAB0023HKDataUpdater(DATABASE_CONFIG)

# 执行数据更新（默认回溯30天）
success = updater.update_data_procedure()

# 或指定回溯天数
success = updater.update_data_procedure(days_back=60)
```

## 数据库表结构

程序会更新 `finance.eab_0023hk` 表，包含以下字段：

- `Date`: 交易日期
- `Open`, `High`, `Low`, `Close`: OHLC价格数据
- `Volume`: 交易量
- `TypicalPrice`: 典型价格 (High+Low+Close)/3
- `MoneyFlow`: 资金流 (TypicalPrice * Volume)
- 其他技术指标字段（预留用于后续计算）

## 日志

程序会生成详细的日志文件 `eab_0023hk_update.log`，记录：
- 数据下载过程
- 数据库操作
- 错误信息
- 更新统计

## 定时任务

可以使用系统的定时任务功能定期运行此脚本：

### Windows (任务计划程序)
创建一个每日运行的任务，执行此Python脚本

### Linux/macOS (crontab)
```bash
# 每天早上9点运行
0 9 * * * /usr/bin/python3 /path/to/update_eab_0023hk_data.py
```

## 注意事项

1. 确保数据库服务正在运行
2. 确保网络连接正常（需要访问Yahoo Finance）
3. 首次运行时会下载最近30天的数据
4. 后续运行只会下载增量数据
5. 程序使用 `ON DUPLICATE KEY UPDATE` 避免重复数据

## 错误处理

程序包含完整的错误处理机制：
- 数据库连接失败时会记录错误并退出
- 网络下载失败时会重试
- 数据插入失败时会回滚事务
- 所有错误都会记录到日志文件中
