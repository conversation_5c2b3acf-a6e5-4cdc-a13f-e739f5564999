using System;
using System.Collections.Generic;

namespace EABDataUpdater.Models
{
    /// <summary>
    /// 股票数据模型
    /// </summary>
    public class StockData
    {
        public DateTime Date { get; set; }
        public decimal Open { get; set; }
        public decimal High { get; set; }
        public decimal Low { get; set; }
        public decimal Close { get; set; }
        public long Volume { get; set; }
        public decimal? AdjClose { get; set; }
        
        // 技术指标
        public decimal TypicalPrice { get; set; }
        public decimal MoneyFlow { get; set; }
        public decimal? PositiveMoneyFlow { get; set; }
        public decimal? NegativeMoneyFlow { get; set; }
        public decimal? MoneyFlowRatio { get; set; }
        public decimal? MFI { get; set; }
        public decimal? Y_Value { get; set; }
        public decimal? X_Value { get; set; }
        public decimal? E_Value { get; set; }
        public decimal? RSI { get; set; }
        public int TradingSignal { get; set; } = 0;
        public int? I { get; set; }
        public decimal? MidPrice { get; set; }
        public byte? Controller { get; set; }
        public decimal? Full_Y { get; set; }
        public decimal? E { get; set; }

        /// <summary>
        /// 计算基本技术指标
        /// </summary>
        public void CalculateBasicIndicators()
        {
            // 计算典型价格 (High + Low + Close) / 3
            TypicalPrice = (High + Low + Close) / 3;
            
            // 计算资金流 TypicalPrice * Volume
            MoneyFlow = TypicalPrice * Volume;
            
            // 计算中间价格 (High + Low) / 2
            MidPrice = (High + Low) / 2;
        }

        /// <summary>
        /// 转换为数据库参数字典
        /// </summary>
        public Dictionary<string, object> ToParameterDictionary()
        {
            return new Dictionary<string, object>
            {
                { "Date", Date.Date },
                { "Open", Open },
                { "High", High },
                { "Low", Low },
                { "Close", Close },
                { "Volume", Volume },
                { "TypicalPrice", TypicalPrice },
                { "MoneyFlow", MoneyFlow },
                { "PositiveMoneyFlow", PositiveMoneyFlow },
                { "NegativeMoneyFlow", NegativeMoneyFlow },
                { "MoneyFlowRatio", MoneyFlowRatio },
                { "MFI", MFI },
                { "Y_Value", Y_Value },
                { "X_Value", X_Value },
                { "E_Value", E_Value },
                { "RSI", RSI },
                { "TradingSignal", TradingSignal },
                { "i", I },
                { "midprice", MidPrice },
                { "Controller", Controller },
                { "Full_Y", Full_Y },
                { "E", E }
            };
        }

        public override string ToString()
        {
            return $"{Date:yyyy-MM-dd} O:{Open:F2} H:{High:F2} L:{Low:F2} C:{Close:F2} V:{Volume:N0}";
        }
    }

    /// <summary>
    /// 股票信息模型
    /// </summary>
    public class StockInfo
    {
        public string Symbol { get; set; }
        public string ShortName { get; set; }
        public string LongName { get; set; }
        public string Exchange { get; set; }
        public string QuoteType { get; set; }
        public string Market { get; set; }
        public string Currency { get; set; }
        public string TimeZone { get; set; }

        public override string ToString()
        {
            return $"{Symbol} - {LongName ?? ShortName} ({Exchange})";
        }
    }

    /// <summary>
    /// 股票报价模型
    /// </summary>
    public class StockQuote
    {
        public string Symbol { get; set; }
        public decimal Price { get; set; }
        public decimal Change { get; set; }
        public decimal ChangePercent { get; set; }
        public long Volume { get; set; }
        public DateTime MarketTime { get; set; }
        public decimal PreviousClose { get; set; }
        public decimal Open { get; set; }
        public decimal DayHigh { get; set; }
        public decimal DayLow { get; set; }
        public decimal? MarketCap { get; set; }
        public decimal? PE { get; set; }
        public decimal? EPS { get; set; }

        public string ChangeDirection => Change >= 0 ? "↑" : "↓";
        public string ChangeColor => Change >= 0 ? "Green" : "Red";

        public override string ToString()
        {
            return $"{Symbol}: {Price:F2} ({Change:+0.00;-0.00;0.00}, {ChangePercent:+0.00;-0.00;0.00}%)";
        }
    }

    /// <summary>
    /// 技术指标计算结果
    /// </summary>
    public class TechnicalIndicators
    {
        public decimal? RSI { get; set; }
        public decimal? MFI { get; set; }
        public decimal? MACD { get; set; }
        public decimal? MACDSignal { get; set; }
        public decimal? MACDHistogram { get; set; }
        public decimal? BollingerUpper { get; set; }
        public decimal? BollingerMiddle { get; set; }
        public decimal? BollingerLower { get; set; }
        public decimal? SMA20 { get; set; }
        public decimal? SMA50 { get; set; }
        public decimal? EMA12 { get; set; }
        public decimal? EMA26 { get; set; }
        public decimal? StochK { get; set; }
        public decimal? StochD { get; set; }
    }

    /// <summary>
    /// 数据更新统计
    /// </summary>
    public class UpdateStatistics
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public int TotalRecords { get; set; }
        public int NewRecords { get; set; }
        public int UpdatedRecords { get; set; }
        public int SkippedRecords { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime? LatestDataDate { get; set; }
        public DateTime? OldestDataDate { get; set; }

        public override string ToString()
        {
            return $"更新统计: 总计{TotalRecords}条, 新增{NewRecords}条, 更新{UpdatedRecords}条, " +
                   $"跳过{SkippedRecords}条, 耗时{Duration.TotalSeconds:F1}秒";
        }
    }
}
