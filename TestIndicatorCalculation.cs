using System;
using System.Collections.Generic;
using EABDataUpdater.Models;
using EABDataUpdater.Services;

namespace EABDataUpdater
{
    /// <summary>
    /// 测试指标计算的示例程序
    /// </summary>
    public class TestIndicatorCalculation
    {
        /// <summary>
        /// 运行测试
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 自定义指标计算测试 ===");
            Console.WriteLine();

            // 创建测试数据
            var testData = CreateTestData();
            
            // 计算指标
            Console.WriteLine("计算指标中...");
            TechnicalIndicatorService.CalculateAllIndicators(testData);
            
            // 显示结果
            DisplayResults(testData);
            
            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        private static List<StockData> CreateTestData()
        {
            var data = new List<StockData>();
            var baseDate = new DateTime(2024, 1, 1);
            
            // 创建5天的测试数据
            var testPrices = new[]
            {
                new { Open = 10.0m, High = 10.5m, Low = 9.8m, Close = 10.2m, Volume = 1000000L },
                new { Open = 10.2m, High = 10.8m, Low = 10.0m, Close = 10.1m, Volume = 1200000L },
                new { Open = 10.1m, High = 10.6m, Low = 9.9m, Close = 10.4m, Volume = 900000L },
                new { Open = 10.4m, High = 10.7m, Low = 10.2m, Close = 10.3m, Volume = 1100000L },
                new { Open = 10.3m, High = 10.9m, Low = 10.1m, Close = 10.6m, Volume = 1300000L }
            };

            for (int i = 0; i < testPrices.Length; i++)
            {
                var price = testPrices[i];
                var stockData = new StockData
                {
                    Date = baseDate.AddDays(i),
                    Open = price.Open,
                    High = price.High,
                    Low = price.Low,
                    Close = price.Close,
                    Volume = price.Volume
                };
                
                // 计算基本指标
                stockData.CalculateBasicIndicators();
                
                // 模拟MFI值（实际应该通过计算得出）
                stockData.MFI = 50 + i * 5; // 50, 55, 60, 65, 70
                
                data.Add(stockData);
            }

            return data;
        }

        /// <summary>
        /// 显示计算结果
        /// </summary>
        private static void DisplayResults(List<StockData> data)
        {
            Console.WriteLine("Row | Date       | Close | MidPrice | Controller | Full_Y  | MFI | E       | i");
            Console.WriteLine("----|------------|-------|----------|------------|---------|-----|---------|---");
            
            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                Console.WriteLine($"{i + 1,3} | {item.Date:yyyy-MM-dd} | {item.Close,5:F1} | {item.MidPrice,8:F2} | {item.Controller,10} | {item.Full_Y,7:F3} | {item.MFI,3:F0} | {item.E,7:F3} | {item.I,2}");
            }
            
            Console.WriteLine();
            Console.WriteLine("计算验证:");
            Console.WriteLine();
            
            // 验证Controller计算
            Console.WriteLine("Controller计算验证:");
            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                var expected = item.Close > item.MidPrice ? 1 : 0;
                var actual = item.Controller ?? 0;
                var status = expected == actual ? "✓" : "✗";
                Console.WriteLine($"Row {i + 1}: Close({item.Close:F1}) > MidPrice({item.MidPrice:F2}) = {item.Close > item.MidPrice} → Controller = {actual} {status}");
            }
            
            Console.WriteLine();
            
            // 验证Full_Y计算
            Console.WriteLine("Full_Y计算验证:");
            int controllerCount = 0;
            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                if (item.Controller == 1) controllerCount++;
                
                decimal expectedFullY = (decimal)controllerCount / (i + 1);
                decimal actualFullY = item.Full_Y ?? 0;
                var status = Math.Abs(expectedFullY - actualFullY) < 0.001m ? "✓" : "✗";
                Console.WriteLine($"Row {i + 1}: Controller=1的总数({controllerCount}) / 行数({i + 1}) = {expectedFullY:F3} → Full_Y = {actualFullY:F3} {status}");
            }
            
            Console.WriteLine();
            
            // 验证E计算
            Console.WriteLine("E计算验证:");
            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                if (item.MFI.HasValue && item.Full_Y.HasValue)
                {
                    decimal mfi = item.MFI.Value;
                    decimal fullY = item.Full_Y.Value;
                    decimal expectedE = (8 * mfi / 100 - 3) * fullY - 3 * mfi / 100 + 1;
                    decimal actualE = item.E ?? 0;
                    var status = Math.Abs(expectedE - actualE) < 0.001m ? "✓" : "✗";
                    Console.WriteLine($"Row {i + 1}: (8*{mfi:F0}/100-3)*{fullY:F3} - 3*{mfi:F0}/100 + 1 = {expectedE:F3} → E = {actualE:F3} {status}");
                }
            }
            
            Console.WriteLine();
            
            // 验证i计算
            Console.WriteLine("i计算验证:");
            for (int i = 0; i < data.Count; i++)
            {
                var item = data[i];
                int expectedI = i + 1;
                int actualI = item.I ?? 0;
                var status = expectedI == actualI ? "✓" : "✗";
                Console.WriteLine($"Row {i + 1}: 行数 = {expectedI} → i = {actualI} {status}");
            }
        }

        /// <summary>
        /// 显示指标说明
        /// </summary>
        public static void ShowIndicatorDescriptions()
        {
            Console.WriteLine("=== 自定义指标说明 ===");
            Console.WriteLine();
            
            var descriptions = CustomIndicatorService.GetIndicatorDescriptions();
            foreach (var desc in descriptions)
            {
                Console.WriteLine($"{desc.Key}: {desc.Value}");
            }
            
            Console.WriteLine();
            Console.WriteLine("详细计算公式:");
            Console.WriteLine("1. Controller = 1 当 Close > MidPrice，否则为 0");
            Console.WriteLine("2. Full_Y = Controller为1的总数 / 当前行数");
            Console.WriteLine("3. E = (8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1");
            Console.WriteLine("4. i = 当前行数（从1开始）");
            Console.WriteLine();
        }
    }
}
