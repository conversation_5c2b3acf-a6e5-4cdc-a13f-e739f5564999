@echo off
echo ========================================
echo EAB 0023.HK 数据更新器 - 编译和运行
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    echo 请从 https://dotnet.microsoft.com/download 下载并安装.NET 6.0 SDK
    pause
    exit /b 1
)

echo 检测到.NET版本:
dotnet --version

echo.
echo 正在还原NuGet包...
dotnet restore

if errorlevel 1 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
dotnet build --configuration Release

if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo.
echo 选择运行模式:
echo 1. GUI界面 (默认)
echo 2. 测试界面
echo 3. 指标查看器
echo 4. 控制台模式
echo 5. 测试指标计算
echo 6. 显示帮助
echo.
set /p choice="请选择 (1-6): "

if "%choice%"=="2" (
    echo 启动测试界面...
    dotnet run --configuration Release test
) else if "%choice%"=="3" (
    echo 启动指标查看器...
    dotnet run --configuration Release indicators
) else if "%choice%"=="4" (
    echo 启动控制台模式...
    dotnet run --configuration Release console
) else if "%choice%"=="5" (
    echo 测试指标计算...
    dotnet run --configuration Release console --test-indicators
) else if "%choice%"=="6" (
    echo 显示帮助...
    dotnet run --configuration Release help
) else (
    echo 启动GUI界面...
    dotnet run --configuration Release
)

echo.
echo 应用程序已退出
pause
