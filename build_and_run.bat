@echo off
echo ========================================
echo EAB 0023.HK 数据更新器 - 编译和运行
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    echo 请从 https://dotnet.microsoft.com/download 下载并安装.NET 6.0 SDK
    pause
    exit /b 1
)

echo 检测到.NET版本:
dotnet --version

echo.
echo 正在还原NuGet包...
dotnet restore

if errorlevel 1 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
dotnet build --configuration Release

if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo.
echo Choose run mode:
echo 1. GUI Interface (default)
echo 2. Test Interface
echo 3. Indicator Viewer
echo 4. Console Mode
echo 5. Test Indicator Calculation
echo 6. Show Help
echo.
set /p choice="Please choose (1-6): "

if "%choice%"=="2" (
    echo Starting test interface...
    dotnet run --configuration Release test
) else if "%choice%"=="3" (
    echo Starting indicator viewer...
    dotnet run --configuration Release indicators
) else if "%choice%"=="4" (
    echo Starting console mode...
    dotnet run --configuration Release console
) else if "%choice%"=="5" (
    echo Testing indicator calculation...
    dotnet run --configuration Release console --test-indicators
) else if "%choice%"=="6" (
    echo Showing help...
    dotnet run --configuration Release help
) else (
    echo Starting GUI interface...
    dotnet run --configuration Release
)

echo.
echo 应用程序已退出
pause
