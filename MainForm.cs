using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using EABDataUpdater.Models;

namespace EABDataUpdater
{
    public partial class MainForm : Form
    {
        private EAB0023HKDataUpdater _dataUpdater;
        private Button btnCalc;
        private TextBox txtLog;
        private Label lblStatus;
        private ProgressBar progressBar;
        private GroupBox groupBoxConfig;
        private TextBox txtServer;
        private TextBox txtDatabase;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private NumericUpDown numDaysBack;

        public MainForm()
        {
            InitializeComponent();
            InitializeDataUpdater();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 主窗体设置
            this.Text = "EAB 0023.HK 数据更新器";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 400);

            // 配置组框
            groupBoxConfig = new GroupBox
            {
                Text = "数据库配置",
                Location = new Point(12, 12),
                Size = new Size(760, 120),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // 服务器
            var lblServer = new Label { Text = "服务器:", Location = new Point(15, 25), Size = new Size(60, 23) };
            txtServer = new TextBox { Text = "localhost", Location = new Point(80, 22), Size = new Size(150, 23) };

            // 数据库
            var lblDatabase = new Label { Text = "数据库:", Location = new Point(250, 25), Size = new Size(60, 23) };
            txtDatabase = new TextBox { Text = "finance", Location = new Point(315, 22), Size = new Size(150, 23) };

            // 用户名
            var lblUsername = new Label { Text = "用户名:", Location = new Point(15, 55), Size = new Size(60, 23) };
            txtUsername = new TextBox { Text = "root", Location = new Point(80, 52), Size = new Size(150, 23) };

            // 密码
            var lblPassword = new Label { Text = "密码:", Location = new Point(250, 55), Size = new Size(60, 23) };
            txtPassword = new TextBox { UseSystemPasswordChar = true, Location = new Point(315, 52), Size = new Size(150, 23) };

            // 回溯天数
            var lblDaysBack = new Label { Text = "回溯天数:", Location = new Point(15, 85), Size = new Size(70, 23) };
            numDaysBack = new NumericUpDown
            {
                Value = 30,
                Minimum = 1,
                Maximum = 365,
                Location = new Point(90, 82),
                Size = new Size(80, 23)
            };

            groupBoxConfig.Controls.AddRange(new Control[]
            {
                lblServer, txtServer, lblDatabase, txtDatabase,
                lblUsername, txtUsername, lblPassword, txtPassword,
                lblDaysBack, numDaysBack
            });

            // 计算按钮
            btnCalc = new Button
            {
                Text = "更新数据",
                Location = new Point(12, 145),
                Size = new Size(120, 35),
                BackColor = Color.LightBlue,
                Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Bold)
            };
            btnCalc.Click += BtnCalc_Click;

            // 状态标签
            lblStatus = new Label
            {
                Text = "就绪",
                Location = new Point(150, 155),
                Size = new Size(400, 23),
                ForeColor = Color.Blue
            };

            // 进度条
            progressBar = new ProgressBar
            {
                Location = new Point(12, 190),
                Size = new Size(760, 23),
                Style = ProgressBarStyle.Marquee,
                Visible = false,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // 日志文本框
            txtLog = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Location = new Point(12, 225),
                Size = new Size(760, 325),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Consolas", 9F),
                BackColor = Color.Black,
                ForeColor = Color.LimeGreen
            };

            // 添加控件到窗体
            this.Controls.AddRange(new Control[]
            {
                groupBoxConfig, btnCalc, lblStatus, progressBar, txtLog
            });

            this.ResumeLayout(false);
        }

        private void InitializeDataUpdater()
        {
            // 初始化时使用默认连接字符串
            UpdateDataUpdater();
        }

        private void UpdateDataUpdater()
        {
            var connectionString = $"Server={txtServer.Text};Database={txtDatabase.Text};Uid={txtUsername.Text};Pwd={txtPassword.Text};CharSet=utf8mb4;";

            _dataUpdater?.Dispose();
            _dataUpdater = new EAB0023HKDataUpdater(connectionString);

            // 订阅事件
            _dataUpdater.OnStatusUpdate += DataUpdater_OnStatusUpdate;
            _dataUpdater.OnError += DataUpdater_OnError;
        }

        private void DataUpdater_OnStatusUpdate(object sender, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object, string>(DataUpdater_OnStatusUpdate), sender, message);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Blue;
            AppendLog($"[INFO] {DateTime.Now:HH:mm:ss} - {message}");
        }

        private void DataUpdater_OnError(object sender, string error)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<object, string>(DataUpdater_OnError), sender, error);
                return;
            }

            lblStatus.Text = $"错误: {error}";
            lblStatus.ForeColor = Color.Red;
            AppendLog($"[ERROR] {DateTime.Now:HH:mm:ss} - {error}");
        }

        private void AppendLog(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AppendLog), message);
                return;
            }

            txtLog.AppendText(message + Environment.NewLine);
            txtLog.ScrollToCaret();
        }

        private async void BtnCalc_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用按钮和显示进度条
                btnCalc.Enabled = false;
                progressBar.Visible = true;

                // 清空日志
                txtLog.Clear();

                AppendLog("=== 开始数据更新 ===");

                // 更新数据更新器配置
                UpdateDataUpdater();

                // 先获取股票信息
                var stockInfo = await _dataUpdater.GetStockInfoAsync();
                if (!string.IsNullOrEmpty(stockInfo.LongName))
                {
                    AppendLog($"股票信息: {stockInfo}");
                }

                // 执行数据更新（使用带统计的版本）
                var stats = await _dataUpdater.UpdateDataProcedureWithStatsAsync((int)numDaysBack.Value);

                if (stats.Success)
                {
                    AppendLog("=== 数据更新成功完成 ===");
                    AppendLog($"更新统计: {stats}");

                    if (stats.LatestDataDate.HasValue)
                    {
                        AppendLog($"最新数据日期: {stats.LatestDataDate:yyyy-MM-dd}");
                    }

                    lblStatus.Text = "数据更新完成";
                    lblStatus.ForeColor = Color.Green;
                }
                else
                {
                    AppendLog("=== 数据更新失败 ===");
                    AppendLog($"错误信息: {stats.ErrorMessage}");
                    lblStatus.Text = "数据更新失败";
                    lblStatus.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"更新过程中发生异常: {ex.Message}";
                AppendLog($"[EXCEPTION] {errorMsg}");
                lblStatus.Text = errorMsg;
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                // 恢复按钮和隐藏进度条
                btnCalc.Enabled = true;
                progressBar.Visible = false;
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _dataUpdater?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
