using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using EABDataUpdater.Models;

namespace EABDataUpdater.Services
{
    /// <summary>
    /// Yahoo Finance数据服务
    /// </summary>
    public class YahooFinanceService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _symbol;

        public YahooFinanceService(string symbol = "0023.HK", int timeoutSeconds = 30)
        {
            _symbol = symbol;
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(timeoutSeconds);

            // 设置User-Agent避免被拒绝
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        /// <summary>
        /// 数据下载事件
        /// </summary>
        public event EventHandler<string> OnStatusUpdate;
        public event EventHandler<string> OnError;

        /// <summary>
        /// 触发状态更新事件
        /// </summary>
        protected virtual void RaiseStatusUpdate(string message)
        {
            OnStatusUpdate?.Invoke(this, message);
        }

        /// <summary>
        /// 触发错误事件
        /// </summary>
        protected virtual void RaiseError(string error)
        {
            OnError?.Invoke(this, error);
        }

        /// <summary>
        /// 获取股票基本信息
        /// </summary>
        public async Task<StockInfo> GetStockInfoAsync()
        {
            try
            {
                RaiseStatusUpdate($"正在获取 {_symbol} 股票信息...");

                var url = $"https://query1.finance.yahoo.com/v1/finance/quoteType/{_symbol}";
                var response = await _httpClient.GetStringAsync(url);
                var jsonData = JObject.Parse(response);

                var result = jsonData["quoteType"]["result"][0];

                var stockInfo = new StockInfo
                {
                    Symbol = _symbol,
                    ShortName = result["shortName"]?.ToString(),
                    LongName = result["longName"]?.ToString(),
                    Exchange = result["exchange"]?.ToString(),
                    QuoteType = result["quoteType"]?.ToString(),
                    Market = result["market"]?.ToString()
                };

                RaiseStatusUpdate($"获取股票信息成功: {stockInfo.LongName}");
                return stockInfo;
            }
            catch (Exception ex)
            {
                RaiseError($"获取股票信息失败: {ex.Message}");
                return new StockInfo { Symbol = _symbol };
            }
        }

        /// <summary>
        /// 下载历史数据
        /// </summary>
        public async Task<List<StockData>> DownloadHistoricalDataAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var stockDataList = new List<StockData>();

            try
            {
                // 设置默认日期
                if (!startDate.HasValue)
                {
                    startDate = DateTime.Now.AddDays(-30);
                }

                if (!endDate.HasValue)
                {
                    endDate = DateTime.Now;
                }

                // 确保endDate包含当天的数据，添加一天
                endDate = endDate.Value.AddDays(1);

                RaiseStatusUpdate($"正在下载 {_symbol} 从 {startDate:yyyy-MM-dd} 到 {endDate:yyyy-MM-dd} 的数据...");

                // 转换为Unix时间戳 (使用UTC时间)
                var startTimestamp = ((DateTimeOffset)startDate.Value.Date).ToUnixTimeSeconds();
                var endTimestamp = ((DateTimeOffset)endDate.Value.Date).ToUnixTimeSeconds();

                // 构建Yahoo Finance API URL
                var url = $"https://query1.finance.yahoo.com/v8/finance/chart/{_symbol}?period1={startTimestamp}&period2={endTimestamp}&interval=1d&includePrePost=false&events=div%2Csplit";

                var response = await _httpClient.GetStringAsync(url);
                var jsonData = JObject.Parse(response);

                // 检查是否有错误
                if (jsonData["chart"]["error"] != null)
                {
                    var error = jsonData["chart"]["error"]["description"]?.ToString();
                    throw new Exception($"Yahoo Finance API错误: {error}");
                }

                var result = jsonData["chart"]["result"][0];

                // 检查是否有数据
                if (result["timestamp"] == null)
                {
                    RaiseStatusUpdate("指定日期范围内没有交易数据");
                    return stockDataList;
                }

                var timestamps = result["timestamp"].ToObject<long[]>();
                var quotes = result["indicators"]["quote"][0];

                var opens = quotes["open"].ToObject<decimal?[]>();
                var highs = quotes["high"].ToObject<decimal?[]>();
                var lows = quotes["low"].ToObject<decimal?[]>();
                var closes = quotes["close"].ToObject<decimal?[]>();
                var volumes = quotes["volume"].ToObject<long?[]>();

                // 处理调整后的收盘价（如果有）
                decimal?[] adjCloses = null;
                if (result["indicators"]["adjclose"] != null)
                {
                    adjCloses = result["indicators"]["adjclose"][0]["adjclose"].ToObject<decimal?[]>();
                }

                for (int i = 0; i < timestamps.Length; i++)
                {
                    // 跳过无效数据
                    if (!opens[i].HasValue || !highs[i].HasValue || !lows[i].HasValue ||
                        !closes[i].HasValue || !volumes[i].HasValue)
                    {
                        continue;
                    }

                    var date = DateTimeOffset.FromUnixTimeSeconds(timestamps[i]).DateTime.Date;

                    var stockData = new StockData
                    {
                        Date = date,
                        Open = opens[i].Value,
                        High = highs[i].Value,
                        Low = lows[i].Value,
                        Close = closes[i].Value,
                        Volume = volumes[i].Value,
                        AdjClose = adjCloses?[i]
                    };

                    stockDataList.Add(stockData);
                }

                RaiseStatusUpdate($"成功下载 {stockDataList.Count} 条记录");
                return stockDataList;
            }
            catch (HttpRequestException ex)
            {
                RaiseError($"网络请求失败: {ex.Message}");
                return stockDataList;
            }
            catch (TaskCanceledException ex)
            {
                RaiseError($"请求超时: {ex.Message}");
                return stockDataList;
            }
            catch (Exception ex)
            {
                RaiseError($"下载Yahoo Finance数据失败: {ex.Message}");
                return stockDataList;
            }
        }

        /// <summary>
        /// 获取最新报价
        /// </summary>
        public async Task<StockQuote> GetLatestQuoteAsync()
        {
            try
            {
                RaiseStatusUpdate($"正在获取 {_symbol} 最新报价...");

                var url = $"https://query1.finance.yahoo.com/v8/finance/chart/{_symbol}?range=1d&interval=1m";
                var response = await _httpClient.GetStringAsync(url);
                var jsonData = JObject.Parse(response);

                var result = jsonData["chart"]["result"][0];
                var meta = result["meta"];

                var quote = new StockQuote
                {
                    Symbol = _symbol,
                    Price = meta["regularMarketPrice"]?.ToObject<decimal>() ?? 0,
                    Change = meta["regularMarketPrice"]?.ToObject<decimal>() - meta["previousClose"]?.ToObject<decimal>() ?? 0,
                    ChangePercent = 0, // 计算百分比变化
                    Volume = meta["regularMarketVolume"]?.ToObject<long>() ?? 0,
                    MarketTime = DateTimeOffset.FromUnixTimeSeconds(meta["regularMarketTime"]?.ToObject<long>() ?? 0).DateTime,
                    PreviousClose = meta["previousClose"]?.ToObject<decimal>() ?? 0,
                    Open = meta["regularMarketOpen"]?.ToObject<decimal>() ?? 0,
                    DayHigh = meta["regularMarketDayHigh"]?.ToObject<decimal>() ?? 0,
                    DayLow = meta["regularMarketDayLow"]?.ToObject<decimal>() ?? 0
                };

                // 计算百分比变化
                if (quote.PreviousClose > 0)
                {
                    quote.ChangePercent = (quote.Change / quote.PreviousClose) * 100;
                }

                RaiseStatusUpdate($"获取最新报价成功: {quote.Price:F2}");
                return quote;
            }
            catch (Exception ex)
            {
                RaiseError($"获取最新报价失败: {ex.Message}");
                return new StockQuote { Symbol = _symbol };
            }
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                RaiseStatusUpdate("测试Yahoo Finance连接...");

                // 尝试获取股票信息
                var stockInfo = await GetStockInfoAsync();

                if (!string.IsNullOrEmpty(stockInfo.LongName))
                {
                    RaiseStatusUpdate("Yahoo Finance连接测试成功");
                    return true;
                }
                else
                {
                    RaiseError("Yahoo Finance连接测试失败：无法获取股票信息");
                    return false;
                }
            }
            catch (Exception ex)
            {
                RaiseError($"Yahoo Finance连接测试失败: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
