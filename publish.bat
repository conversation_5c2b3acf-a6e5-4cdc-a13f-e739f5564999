@echo off
echo ========================================
echo EAB 0023.HK 数据更新器 - 发布
echo ========================================
echo.

REM 检查.NET是否安装
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: .NET SDK未安装或不在PATH中
    pause
    exit /b 1
)

echo 正在发布应用程序...
echo.

REM 发布为独立可执行文件
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output ./publish

if errorlevel 1 (
    echo 错误: 发布失败
    pause
    exit /b 1
)

echo.
echo 发布成功！
echo 可执行文件位置: ./publish/EABDataUpdater.exe
echo.
echo 您可以将publish文件夹复制到任何Windows电脑上运行
echo 无需安装.NET运行时
echo.
pause
